# coding: utf-8
# 最终版高级Walmart爬虫 - 真正有效的反爬虫绕过

import requests
import time
import random
import json
import logging
import re
from typing import Dict, List, Optional, Tuple
import pandas as pd
from datetime import datetime
import traceback
import urllib3
from fake_useragent import UserAgent
import uuid

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class FinalProxyManager:
    """最终版代理管理器"""
    
    def __init__(self, proxy_file='proxies.txt'):
        self.proxy_file = proxy_file
        self.proxies = []
        self.failed_proxies = set()
        self.current_index = 0
        self.load_proxies()
    
    def load_proxies(self):
        """加载代理"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            proxy_blocks = content.strip().split('\n\n')
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if '协议:' in line:
                        proxy_info['protocol'] = line.split('协议:')[1].strip()
                    elif '地址:' in line:
                        proxy_info['host'] = line.split('地址:')[1].strip()
                    elif '端口:' in line:
                        proxy_info['port'] = line.split('端口:')[1].strip()
                    elif '用户名:' in line:
                        proxy_info['username'] = line.split('用户名:')[1].strip()
                    elif '密码:' in line:
                        proxy_info['password'] = line.split('密码:')[1].strip()
                
                if len(proxy_info) == 5:
                    self.proxies.append(proxy_info)
            
            logging.info(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            logging.error(f"加载代理文件失败: {str(e)}")
            self.proxies = []
    
    def get_next_proxy(self):
        """获取下一个可用代理"""
        if not self.proxies:
            return None
        
        # 过滤失效代理
        available_proxies = [p for p in self.proxies if self._proxy_key(p) not in self.failed_proxies]
        
        if not available_proxies:
            logging.warning("所有代理都已失效，重置失效列表")
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        if not available_proxies:
            return None
        
        # 轮询选择代理
        proxy = available_proxies[self.current_index % len(available_proxies)]
        self.current_index += 1
        
        return self._format_proxy(proxy)
    
    def _format_proxy(self, proxy_info):
        """格式化代理"""
        if proxy_info['protocol'].lower() == 'socks5':
            proxy_url = f"socks5://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
        else:
            proxy_url = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
        
        return {
            'http': proxy_url,
            'https': proxy_url,
            'info': proxy_info
        }
    
    def mark_proxy_failed(self, proxy_info):
        """标记代理失效"""
        if proxy_info and 'info' in proxy_info:
            proxy_key = self._proxy_key(proxy_info['info'])
            self.failed_proxies.add(proxy_key)
            logging.warning(f"标记代理失效: {proxy_info['info']['host']}:{proxy_info['info']['port']}")
    
    def _proxy_key(self, proxy_info):
        """生成代理唯一标识"""
        return f"{proxy_info['host']}:{proxy_info['port']}"
    
    def get_stats(self):
        """获取统计信息"""
        available = len([p for p in self.proxies if self._proxy_key(p) not in self.failed_proxies])
        return {
            'total': len(self.proxies),
            'available': available,
            'failed': len(self.failed_proxies)
        }


class FinalWalmartScraper:
    """最终版Walmart爬虫"""
    
    def __init__(self):
        self.proxy_manager = FinalProxyManager()
        self.ua = UserAgent()
        self.session = None
        self.current_proxy = None
        self.pxvid_tokens = []
        self.create_session()
    
    def create_session(self):
        """创建会话"""
        self.session = requests.Session()
        
        # 设置基础配置
        self.session.verify = False
        self.session.timeout = 15
        
        # 获取代理
        self.current_proxy = self.proxy_manager.get_next_proxy()
        if self.current_proxy:
            self.session.proxies = self.current_proxy
            logging.info(f"使用代理: {self.current_proxy['info']['host']}:{self.current_proxy['info']['port']}")
    
    def get_headers(self):
        """生成请求头"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
        }
    
    def get_pxvid_token(self):
        """获取或生成pxvid令牌"""
        try:
            # 尝试从主页获取令牌
            headers = self.get_headers()
            response = self.session.get('https://www.walmart.com', headers=headers, timeout=10)
            
            if response.status_code == 200:
                # 查找现有令牌
                token_match = re.search(r'_pxvid["\']?\s*[:=]\s*["\']([^"\']+)', response.text)
                if token_match:
                    token = token_match.group(1)
                    logging.info(f"从页面获取令牌: {token[:15]}...")
                    return token
            
        except Exception as e:
            logging.debug(f"从页面获取令牌失败: {str(e)}")
        
        # 生成新令牌
        token = str(uuid.uuid4())
        logging.info(f"生成新令牌: {token[:15]}...")
        return token
    
    def detect_anti_crawler(self, response):
        """检测反爬虫"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True, f"状态码: {response.status_code}"
        
        # 检查内容
        content = response.text.lower()
        keywords = ['queue-it', 'captcha', 'blocked', 'access denied', 'rate limit', 'security check']
        
        for keyword in keywords:
            if keyword in content:
                return True, f"关键词: {keyword}"
        
        # 检查内容长度
        if len(content) < 1000:
            return True, "内容过短"
        
        return False, "正常"
    
    def process_product(self, product_id, max_retries=3):
        """处理单个商品"""
        url = f"https://www.walmart.com/ip/{product_id}"
        
        for attempt in range(max_retries):
            try:
                logging.info(f"处理商品 {product_id}, 尝试 {attempt + 1}")
                
                # 随机延时
                delay = random.uniform(5, 12)
                time.sleep(delay)
                
                # 准备请求
                headers = self.get_headers()
                cookies = {}
                
                # 添加令牌
                if not self.pxvid_tokens or random.random() < 0.3:  # 30%概率获取新令牌
                    token = self.get_pxvid_token()
                    if token:
                        self.pxvid_tokens.append(token)
                
                if self.pxvid_tokens:
                    cookies['_pxvid'] = random.choice(self.pxvid_tokens)
                
                # 发送请求
                response = self.session.get(url, headers=headers, cookies=cookies)
                
                # 检测反爬虫
                is_detected, reason = self.detect_anti_crawler(response)
                
                if not is_detected:
                    logging.info(f"商品 {product_id} 请求成功")
                    return self.extract_product_data(response, product_id)
                
                logging.warning(f"商品 {product_id} 尝试 {attempt + 1}: 检测到反爬虫 - {reason}")
                
                # 如果检测到反爬虫，切换代理
                if attempt < max_retries - 1:
                    self.switch_proxy()
                    time.sleep(random.uniform(10, 20))  # 更长的延时
                
            except Exception as e:
                logging.error(f"商品 {product_id} 尝试 {attempt + 1} 异常: {str(e)}")
                if attempt < max_retries - 1:
                    self.switch_proxy()
        
        return {'ID': product_id, 'Brand': '处理失败'}
    
    def switch_proxy(self):
        """切换代理"""
        logging.info("切换代理...")
        
        # 标记当前代理失效
        if self.current_proxy:
            self.proxy_manager.mark_proxy_failed(self.current_proxy)
        
        # 获取新代理
        self.current_proxy = self.proxy_manager.get_next_proxy()
        if self.current_proxy:
            self.session.proxies = self.current_proxy
            logging.info(f"切换到新代理: {self.current_proxy['info']['host']}:{self.current_proxy['info']['port']}")
        else:
            logging.error("无可用代理")
    
    def extract_product_data(self, response, product_id):
        """提取商品数据"""
        row_data = {'ID': product_id}
        
        try:
            html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')
            
            # 提取基本信息
            patterns = {
                'Brand': r'"Brand","name":"(.*?)"',
                'Title': r'"productName":"(.*?)"',
                'LongDescription': r'"longDescription":"(.*?)","shortDescription',
                'ProductDetails': r'"shortDescription":"(.*?)","fulfillmentType":',
                'Link': r'\{"@type":"Offer","url":"(.*?)",',
                'zy': r',"sellerDisplayName":"(.*?)","',
                'price': r'"priceCurrency":"USD","price":(.*?),"',
                'left': r'"usecase":"SHIPPING","value":"(.*?)",',
                'wfs': r'"wfsEnabled":(.*?),',
                'yunfei': r'"shipPrice":{"price":(.*?),"',
                'pingfen': r'@type":"AggregateRating","ratingValue":(.*?),"',
                'pinglun': r'totalReviewsCountAsString":"(.*?)","'
            }
            
            for key, pattern in patterns.items():
                try:
                    matches = re.findall(pattern, html)
                    row_data[key] = matches[0] if matches else ''
                except:
                    row_data[key] = ''
            
            # 检查商品是否有效
            if row_data['zy'] == '':
                row_data['Brand'] = 'ID失效或缺货'
            
            logging.info(f"商品 {product_id} 数据提取成功")
            
        except Exception as e:
            logging.error(f"商品 {product_id} 数据提取失败: {str(e)}")
            row_data['Brand'] = '数据提取失败'
        
        return row_data
    
    def process_batch(self, product_ids):
        """批量处理商品"""
        results = []
        
        logging.info(f"开始批量处理 {len(product_ids)} 个商品")
        
        for i, product_id in enumerate(product_ids, 1):
            logging.info(f"进度: {i}/{len(product_ids)}")
            
            result = self.process_product(str(product_id))
            results.append(result)
            
            # 显示进度
            if result['Brand'] not in ['处理失败', 'ID失效或缺货', '数据提取失败']:
                logging.info(f"✅ 成功: {product_id} - {result['Brand']}")
            else:
                logging.warning(f"❌ 失败: {product_id} - {result['Brand']}")
            
            # 显示代理状态
            stats = self.proxy_manager.get_stats()
            logging.info(f"代理状态: {stats}")
        
        return results
    
    def save_results(self, results, filename='final_results.xlsx'):
        """保存结果"""
        if results:
            df = pd.DataFrame(results)
            df.to_excel(filename, index=False)
            logging.info(f"结果已保存到: {filename}")
            
            # 统计
            success_count = sum(1 for r in results if r['Brand'] not in ['处理失败', 'ID失效或缺货', '数据提取失败'])
            success_rate = (success_count / len(results)) * 100
            logging.info(f"成功率: {success_rate:.1f}% ({success_count}/{len(results)})")


def test_final_scraper():
    """测试最终版爬虫"""
    print("🚀 测试最终版Walmart高级爬虫")
    print("=" * 50)
    
    scraper = FinalWalmartScraper()
    
    # 测试商品ID
    test_ids = ["123456789", "987654321", "555666777"]
    
    print(f"📋 测试商品ID: {test_ids}")
    
    # 处理商品
    results = scraper.process_batch(test_ids)
    
    # 保存结果
    scraper.save_results(results, 'test_final_results.xlsx')
    
    print("\n📊 测试完成!")
    return results

def main():
    """主程序"""
    print("🎯 最终版Walmart高级反爬虫系统")
    print("=" * 60)
    
    print("\n选择操作:")
    print("1. 测试系统")
    print("2. 处理Excel文件")
    print("3. 手动输入商品ID")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == '1':
        test_final_scraper()
    
    elif choice == '2':
        excel_file = input("请输入Excel文件路径 (默认: 队列.xlsx): ").strip()
        if not excel_file:
            excel_file = '队列.xlsx'
        
        try:
            df = pd.read_excel(excel_file)
            if 'ID' not in df.columns:
                print("Excel文件必须包含'ID'列")
                return
            
            product_ids = df['ID'].astype(str).tolist()
            print(f"找到 {len(product_ids)} 个商品ID")
            
            scraper = FinalWalmartScraper()
            results = scraper.process_batch(product_ids)
            scraper.save_results(results)
            
        except Exception as e:
            print(f"处理Excel文件失败: {str(e)}")
    
    elif choice == '3':
        ids_input = input("请输入商品ID (用逗号分隔): ").strip()
        product_ids = [id.strip() for id in ids_input.split(',') if id.strip()]
        
        if product_ids:
            scraper = FinalWalmartScraper()
            results = scraper.process_batch(product_ids)
            scraper.save_results(results)
        else:
            print("未输入有效的商品ID")
    
    else:
        print("无效选择")

if __name__ == '__main__':
    main()
