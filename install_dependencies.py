# coding: utf-8
# 依赖安装脚本

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    print("🔧 开始安装依赖包...")
    print("=" * 50)
    
    # 必需的包列表
    packages = [
        "pandas>=1.5.0",
        "requests>=2.28.0", 
        "selenium>=4.15.0",
        "undetected-chromedriver>=3.5.0",
        "fake-useragent>=1.4.0",
        "urllib3>=1.26.0",
        "openpyxl>=3.1.0",
        "python-dotenv>=1.0.0",
        "webdriver-manager>=4.0.0"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"\n📦 安装 {package}...")
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n" + "=" * 50)
    
    if failed_packages:
        print("❌ 以下包安装失败:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        print("\n请手动安装失败的包:")
        print(f"pip install {' '.join(failed_packages)}")
    else:
        print("🎉 所有依赖包安装成功!")
    
    print("\n📋 安装完成后请确保:")
    print("1. Chrome浏览器已安装")
    print("2. proxies.txt文件存在且格式正确")
    print("3. 网络连接正常")

if __name__ == '__main__':
    main()
