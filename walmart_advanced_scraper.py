# coding: utf-8
# Walmart高级爬虫 - 真正有效的反爬虫绕过系统

import pandas as pd
import logging
import time
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict
import traceback
import os

from advanced_anti_detection import (
    AdvancedProxyManager, 
    UndetectedBrowserManager, 
    WalmartAntiDetectionHandler
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('walmart_advanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class WalmartAdvancedScraper:
    """Walmart高级爬虫系统"""
    
    def __init__(self, proxy_file='proxies.txt'):
        self.proxy_manager = AdvancedProxyManager(proxy_file)
        self.results = []
        self.failed_ids = []
        self.success_count = 0
        self.total_processed = 0
        
    def process_single_id(self, product_id: str, max_retries=3) -> Dict:
        """处理单个商品ID"""
        browser_manager = None
        handler = None
        
        try:
            logging.info(f"开始处理商品ID: {product_id}")
            
            # 创建浏览器管理器和处理器
            browser_manager = UndetectedBrowserManager(self.proxy_manager)
            handler = WalmartAntiDetectionHandler(browser_manager, self.proxy_manager)
            
            # 初始化会话
            if not handler.initialize_session():
                logging.error(f"ID {product_id}: 会话初始化失败")
                return {'ID': product_id, 'Brand': '会话初始化失败'}
            
            # 构建商品URL
            product_url = f"https://www.walmart.com/ip/{product_id}"
            
            # 智能加载页面
            success, page_source = handler.smart_page_load(product_url, max_retries)
            
            if not success:
                logging.error(f"ID {product_id}: 页面加载失败")
                return {'ID': product_id, 'Brand': '页面加载失败'}
            
            # 提取商品数据
            product_data = handler.extract_product_data(page_source, product_id)
            
            # 记录代理使用结果
            if browser_manager.current_proxy:
                self.proxy_manager.record_proxy_result(
                    browser_manager.current_proxy, 
                    success=True, 
                    response_time=5.0
                )
            
            logging.info(f"ID {product_id}: 处理成功")
            return product_data
            
        except Exception as e:
            logging.error(f"ID {product_id}: 处理异常 - {str(e)}")
            logging.debug(traceback.format_exc())
            
            # 记录代理失败
            if browser_manager and browser_manager.current_proxy:
                self.proxy_manager.record_proxy_result(
                    browser_manager.current_proxy, 
                    success=False
                )
            
            return {'ID': product_id, 'Brand': f'处理异常: {str(e)}'}
            
        finally:
            # 清理资源
            if handler:
                handler.cleanup()
    
    def process_ids_batch(self, product_ids: List[str], max_workers=2):
        """批量处理商品ID"""
        logging.info(f"开始批量处理 {len(product_ids)} 个商品ID，并发数: {max_workers}")
        
        self.total_processed = len(product_ids)
        processed_count = 0
        
        # 使用较低的并发数避免触发反爬虫
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_id = {
                executor.submit(self.process_single_id, product_id): product_id 
                for product_id in product_ids
            }
            
            # 处理结果
            for future in as_completed(future_to_id):
                product_id = future_to_id[future]
                processed_count += 1
                
                try:
                    result = future.result()
                    self.results.append(result)
                    
                    # 检查是否成功
                    if result.get('Brand') and result['Brand'] not in ['ID失效或缺货', '会话初始化失败', '页面加载失败']:
                        if not result['Brand'].startswith('处理异常'):
                            self.success_count += 1
                        else:
                            self.failed_ids.append(product_id)
                    else:
                        self.failed_ids.append(product_id)
                    
                    # 进度报告
                    success_rate = (self.success_count / processed_count) * 100
                    logging.info(f"进度: {processed_count}/{self.total_processed} "
                               f"成功率: {success_rate:.1f}% "
                               f"当前成功: {self.success_count}")
                    
                    # 代理池状态
                    proxy_stats = self.proxy_manager.get_stats()
                    logging.info(f"代理池状态: {proxy_stats}")
                    
                except Exception as e:
                    logging.error(f"处理结果异常 {product_id}: {str(e)}")
                    self.failed_ids.append(product_id)
                
                # 批次间延时
                time.sleep(random.uniform(2, 5))
    
    def save_results(self, output_file='walmart_results_advanced.xlsx'):
        """保存结果到Excel"""
        if not self.results:
            logging.warning("没有结果可保存")
            return
        
        try:
            df = pd.DataFrame(self.results)
            df.to_excel(output_file, index=False)
            logging.info(f"结果已保存到: {output_file}")
            
            # 保存统计信息
            stats = {
                'total_processed': self.total_processed,
                'success_count': self.success_count,
                'failed_count': len(self.failed_ids),
                'success_rate': (self.success_count / self.total_processed * 100) if self.total_processed > 0 else 0,
                'failed_ids': self.failed_ids,
                'timestamp': datetime.now().isoformat()
            }
            
            with open('scraping_stats.json', 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            logging.info(f"统计信息: 总计 {self.total_processed}, 成功 {self.success_count}, "
                        f"失败 {len(self.failed_ids)}, 成功率 {stats['success_rate']:.1f}%")
            
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")
    
    def get_stats(self):
        """获取代理池统计"""
        available_proxies = len([p for p in self.proxy_manager.proxies 
                               if self.proxy_manager._proxy_key(p) not in self.proxy_manager.failed_proxies])
        
        return {
            'total': len(self.proxy_manager.proxies),
            'available': available_proxies,
            'failed': len(self.proxy_manager.failed_proxies)
        }


def test_single_product():
    """测试单个商品"""
    print("🧪 测试单个商品处理...")
    
    scraper = WalmartAdvancedScraper()
    test_id = "123456789"  # 测试ID
    
    result = scraper.process_single_id(test_id)
    print(f"测试结果: {result}")
    
    return result.get('Brand') not in ['会话初始化失败', '页面加载失败']


def trigger_and_bypass_test():
    """触发反爬虫并测试绕过"""
    print("🎯 触发反爬虫并测试绕过...")
    
    scraper = WalmartAdvancedScraper()
    
    # 使用多个测试ID快速触发反爬虫
    test_ids = ["111111111", "222222222", "333333333", "444444444", "555555555"]
    
    print("第一阶段: 快速请求触发反爬虫...")
    scraper.process_ids_batch(test_ids[:3], max_workers=3)  # 高并发触发
    
    print("第二阶段: 使用高级技术绕过...")
    time.sleep(10)  # 等待一段时间
    scraper.process_ids_batch(test_ids[3:], max_workers=1)  # 低并发绕过
    
    scraper.save_results('trigger_bypass_test.xlsx')
    return len(scraper.results) > 0


def main():
    """主程序"""
    print("🚀 Walmart高级反爬虫绕过系统")
    print("=" * 50)
    
    while True:
        print("\n选择操作:")
        print("1. 测试单个商品")
        print("2. 触发反爬虫并测试绕过")
        print("3. 处理Excel文件中的商品ID")
        print("4. 查看代理池状态")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == '1':
            test_single_product()
            
        elif choice == '2':
            trigger_and_bypass_test()
            
        elif choice == '3':
            excel_file = input("请输入Excel文件路径 (默认: 队列.xlsx): ").strip()
            if not excel_file:
                excel_file = '队列.xlsx'
            
            if not os.path.exists(excel_file):
                print(f"文件不存在: {excel_file}")
                continue
            
            try:
                df = pd.read_excel(excel_file)
                if 'ID' not in df.columns:
                    print("Excel文件必须包含'ID'列")
                    continue
                
                product_ids = df['ID'].astype(str).tolist()
                print(f"找到 {len(product_ids)} 个商品ID")
                
                max_workers = int(input("并发数 (建议1-2): ") or "1")
                
                scraper = WalmartAdvancedScraper()
                scraper.process_ids_batch(product_ids, max_workers)
                scraper.save_results()
                
            except Exception as e:
                print(f"处理Excel文件失败: {str(e)}")
        
        elif choice == '4':
            scraper = WalmartAdvancedScraper()
            stats = scraper.get_stats()
            print(f"代理池状态: {stats}")
            
        elif choice == '5':
            print("退出程序")
            break
        
        else:
            print("无效选择，请重试")


if __name__ == '__main__':
    import random
    main()
