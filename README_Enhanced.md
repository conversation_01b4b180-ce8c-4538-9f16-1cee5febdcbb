# 增强版Walmart爬虫 - 使用说明

## 概述

这是一个增强版的Walmart商品信息爬虫，集成了代理池管理和反爬虫检测功能。主要特性包括：

- **代理池管理**: 自动从`proxies.txt`加载SOCKS5代理，支持失效代理自动切换
- **反爬虫检测**: 智能检测反爬虫机制并自动应对
- **令牌管理**: 自动获取和管理Walmart的_pxvid令牌
- **持续测试**: 支持不断测试直到成功绕过检测
- **详细日志**: 完整的操作日志记录

## 文件结构

```
├── 1.py                    # 增强版主程序
├── proxies.txt            # SOCKS5代理列表
├── 令牌.xlsx              # pxvid令牌文件
├── 能跑多少跑多少1.xlsx    # 待查询的ID列表
├── test_enhanced.py       # 测试脚本
├── README_Enhanced.md     # 使用说明
└── crawler.log           # 运行日志
```

## 主要功能

### 1. 代理池管理 (ProxyManager)

- 自动解析`proxies.txt`中的代理信息
- 随机选择可用代理
- 自动标记和排除失效代理
- 实时统计代理使用情况

### 2. 反检测管理 (AntiDetectionManager)

- 生成随机化的请求头
- 自动获取Walmart令牌
- 检测反爬虫响应
- 智能应对策略

### 3. 增强版处理流程

1. **触发反检测**: 故意触发反爬虫机制以测试检测能力
2. **智能重试**: 检测到反爬虫时自动切换代理和令牌
3. **持续测试**: 循环测试直到成功绕过检测
4. **详细记录**: 记录每次尝试的详细信息

## 使用方法

### 1. 准备工作

确保以下文件存在且格式正确：

- `proxies.txt`: SOCKS5代理列表
- `令牌.xlsx`: 包含pxvid列的Excel文件
- `能跑多少跑多少1.xlsx`: 包含ID列的Excel文件

### 2. 运行程序

#### 正常运行模式
```bash
python 1.py
```

程序会提示选择运行模式：
- 选择 `1`: 使用增强版功能（推荐）
- 选择 `2`: 使用原版功能

#### 测试模式
```bash
python 1.py test
```

#### 独立测试
```bash
python test_enhanced.py
```

### 3. 运行流程

增强版运行流程：

1. **初始化**: 加载代理池和反检测管理器
2. **触发反检测**: 故意触发反爬虫机制
3. **数据采集**: 使用智能重试机制采集数据
4. **结果保存**: 保存到Excel文件

## 配置说明

### proxies.txt 格式

```
协议: socks5
地址: 107.174.92.132
端口: 48361
用户名: 1BHVeuZ1WM
密码: 65tJbpVstb

协议: socks5
地址: 172.245.95.202
端口: 43562
用户名: Bix5uohS6v
密码: uLNPaIK5ov
```

### 主要参数

- `max_workers`: 并发线程数（默认5，避免过度触发反爬虫）
- `max_retries`: 每个ID的最大重试次数（默认5）
- `timeout`: 请求超时时间（默认15秒）

## 日志说明

程序会生成详细的日志文件`crawler.log`，包含：

- 代理池状态变化
- 令牌获取过程
- 反爬虫检测结果
- 每个ID的处理过程
- 错误和异常信息

## 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理格式是否正确
   - 验证代理是否有效
   - 查看日志中的具体错误信息

2. **令牌获取失败**
   - 检查网络连接
   - 尝试更换代理
   - 查看是否被反爬虫拦截

3. **反爬虫检测**
   - 程序会自动处理
   - 可以调整请求间隔
   - 检查代理池是否充足

### 调试技巧

1. 使用测试模式验证功能
2. 查看详细日志分析问题
3. 单独测试代理和令牌获取
4. 调整并发数和重试参数

## 性能优化

- 使用高质量的代理服务
- 适当调整并发数
- 定期更新代理列表
- 监控成功率并调整策略

## 注意事项

1. 请遵守网站的robots.txt和使用条款
2. 合理控制请求频率，避免对服务器造成压力
3. 定期更新代理列表以保持高成功率
4. 建议在测试环境中先验证功能

## 更新日志

- v2.0: 集成代理池和反爬虫功能
- v1.0: 基础爬虫功能
