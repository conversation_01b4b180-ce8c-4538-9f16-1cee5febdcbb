2025-07-28 23:56:08,515 - INFO - 成功加载 53 个代理
2025-07-28 23:56:08,641 - INFO - 使用代理: **************:48361
2025-07-28 23:56:08,642 - INFO - 初始化Walmart会话...
2025-07-28 23:56:12,082 - INFO - 主页访问成功
2025-07-28 23:56:12,083 - INFO - 尝试获取pxvid令牌...
2025-07-28 23:56:13,822 - INFO - 生成pxvid令牌: 45c33944-db72-4...
2025-07-28 23:56:13,823 - INFO - 尝试 1: 请求 https://www.walmart.com/ip/123456789
2025-07-28 23:56:19,353 - WARNING - 尝试 1: 检测到反爬虫 - 关键词检测: captcha
2025-07-28 23:56:19,354 - INFO - 切换代理和会话...
2025-07-28 23:56:19,373 - WARNING - 标记代理失效: **************:48361
2025-07-28 23:56:19,431 - INFO - 使用代理: **************:43562
2025-07-28 23:56:19,434 - INFO - 尝试获取pxvid令牌...
2025-07-28 23:56:24,754 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DE5C0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /api/v1/services/px/px.js
2025-07-28 23:56:32,225 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DEE30>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /api/v1/services/px/px.js
2025-07-28 23:56:41,539 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DF160>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /api/v1/services/px/px.js
2025-07-28 23:56:46,958 - ERROR - 请求失败: SOCKSHTTPSConnectionPool(host='www.walmart.com', port=443): Max retries exceeded with url: /api/v1/services/px/px.js (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DF490>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-28 23:56:46,959 - ERROR - 获取pxvid令牌失败: SOCKSHTTPSConnectionPool(host='www.walmart.com', port=443): Max retries exceeded with url: /api/v1/services/px/px.js (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DF490>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-28 23:57:05,096 - INFO - 尝试 2: 请求 https://www.walmart.com/ip/123456789
2025-07-28 23:57:14,914 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DF970>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/123456789
2025-07-28 23:57:22,303 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DFCD0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/123456789
2025-07-28 23:57:31,618 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2F8040>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/123456789
2025-07-28 23:57:37,013 - ERROR - 请求失败: SOCKSHTTPSConnectionPool(host='www.walmart.com', port=443): Max retries exceeded with url: /ip/123456789 (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DE9B0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-28 23:57:37,013 - ERROR - 尝试 2: 请求异常 - SOCKSHTTPSConnectionPool(host='www.walmart.com', port=443): Max retries exceeded with url: /ip/123456789 (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DE9B0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-28 23:57:37,014 - INFO - 切换代理和会话...
2025-07-28 23:57:37,014 - WARNING - 标记代理失效: **************:43562
2025-07-28 23:57:37,052 - INFO - 使用代理: ***************:16842
2025-07-28 23:57:37,053 - INFO - 尝试 3: 请求 https://www.walmart.com/ip/123456789
2025-07-28 23:57:45,902 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2DECE0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/123456789
2025-07-28 23:57:53,219 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2F83A0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/123456789
2025-07-28 23:58:02,529 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2F86A0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/123456789
2025-07-28 23:58:07,858 - ERROR - 请求失败: SOCKSHTTPSConnectionPool(host='www.walmart.com', port=443): Max retries exceeded with url: /ip/123456789 (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2F89A0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-28 23:58:07,858 - ERROR - 尝试 3: 请求异常 - SOCKSHTTPSConnectionPool(host='www.walmart.com', port=443): Max retries exceeded with url: /ip/123456789 (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2F89A0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-28 23:58:07,859 - INFO - 尝试 1: 请求 https://www.walmart.com/ip/987654321
2025-07-28 23:58:17,901 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000022C7B2F8E20>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /ip/987654321
