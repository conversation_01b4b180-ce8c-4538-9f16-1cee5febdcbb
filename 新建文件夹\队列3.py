# coding: utf-8
# Project:fenggong_imagelink
# File:1.py
# Author:白茶
# Date: 2025/4/3 上午10:00
# IDE:PyCharm

import concurrent
import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
from concurrent.futures import ThreadPoolExecutor
import json

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_id():
    df = pd.read_excel('1.xlsx')
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids

def get_ip_address():
    proxyAddr = "overseas-hk.tunnel.qg.net:14340"
    authKey = "013CG5LN"
    password = "094BF239ECD2"
    proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
        "user": authKey,
        "password": password,
        "server": proxyAddr,
    }
    proxies = {
        "http": proxyUrl,
        "https": proxyUrl,
    }
    return proxies


def md5_encrypt(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()


def Headers():
    # 版本号
    v1 = random.randint(100, 134)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)
    # google环境
    headers = {
        'name': '',
        # "domain": "www.walmart.com",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'sec-ch-ua': f'"Chromium";v="{v1}", "Not:A-Brand";v="{v2}", "Brave";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36"
    }
    return headers


def _pxvid_get():
    url = f"https://www.walmart.com/ip/{random.randint(1, 10000)}{str(time.time())}{str(random.randint(1, 10000))}{str(time.time())}{str(random.randint(1, 10000))}"
    for attempt in range(3):
        try:
            response = requests.head(url, headers=Headers(), proxies=get_ip_address(), verify=False, timeout=10)
            # print(response.cookies)
            _pxvid = response.cookies.get('_pxhd').split(':')[-1]
            # print("令牌:", _pxvid)
            return _pxvid
        except:
            if attempt == 2:
                raise


def process_id(id):
    row_data = {'ID': id}
    max_retries = 3

    for attempt in range(max_retries):
        try:
            url = f"https://www.walmart.com/ip/{id}"

            cookie = {
                "_pxvid": _pxvid_get(),

            }
            time.sleep(11)

            response = requests.get(url, cookies=cookie, headers=Headers(), proxies=get_ip_address(), timeout=10)

            if response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR':
                html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')

                # 提取产品信息
                row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
                row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"',html) else ''
                row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
                row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
                row_data['Link'] = (re.findall('{"@type":"Offer","url":"(.*?)",', html)[0] + '?classType=REGULAR&from=/search') if re.findall( '{"@type":"Offer","url":"(.*?)",', html) else ''
                row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","',html)[0] if re.findall(',"sellerDisplayName":"(.*?)","',html) else ''
                row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"',html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"(.*?),"',html) else ''
                row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",',html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",',html) else ''
                row_data['wfs'] = re.findall('"wfsEnabled":(.*?),',html)[0] if re.findall('"wfsEnabled":(.*?),',html) else ''
                row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"',html)[0] if re.findall('"shipPrice":{"price":(.*?),"',html) else ''
                row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"',html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"',html) else ''
                row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","',html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","',html) else ''
                # 提取图片链接
                image_result = {'ID': id}
                all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
                if all_images_match:
                    try:
                        images_data = json.loads(f'[{all_images_match.group(1)}]')
                        for idx, img in enumerate(images_data[:20], 1):  # 最多取20张图片
                            if 'url' in img:
                                image_result[f'Image_{idx}'] = img['url']
                    except Exception as e:
                        print(f"图片解析失败 ID: {id} - {str(e)}")

                # 合并数据到同一行
                row_data.update(image_result)
                break

            else:
                print(f"ID {id} 状态码 {response.status_code}, 第{attempt + 1}次重试")
                if response.status_code == 404 and attempt == 2:
                    row_data['Brand'] = 'ID失效'
        except Exception as e:
            print(f"ID {id} 请求异常: {str(e)}，第 {attempt + 1} 次重试")
            if attempt == 2:
                row_data['Brand'] = '请求失败'
    print(row_data)
    return row_data


def request_data():
    ids = get_id()
    all_results = []

    with ThreadPoolExecutor(max_workers=100) as executor:
        futures = [executor.submit(process_id, id) for id in ids]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            all_results.append(result)
            print(f"已完成ID {result['ID']} 的数据采集")

    # 动态生成列（产品信息列 + 图片列）
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs', 'yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    df.to_excel('combined_results-usItemIds410-2.xlsx', index=False)
    print("结果已保存到 combined_results-usItemIds410-2.xlsx")


if __name__ == '__main__':
    time1 = time.time()
    request_data()
    print("用时：", time.time() - time1)