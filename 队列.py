# coding: utf-8
# Project:fenggong_imagelink
# File:1.py
# Author:白茶
# Date: 2025/4/3 上午10:00
# IDE:PyCharm

import concurrent
import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
import json
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import traceback

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_id():
    df = pd.read_excel('1.xlsx')
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids


def get_ip_address():
    proxyAddr = "overseas.tunnel.qg.net:15655"
    authKey = "0MQGW8CU"
    password = "99AF4C18800B"
    proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
        "user": authKey,
        "password": password,
        "server": proxyAddr,
    }
    proxies = {
        "http": proxyUrl,
        "https": proxyUrl,
    }
    return proxies


def md5_encrypt(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()


def Headers():
    # 版本号
    v1 = random.randint(100, 134)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)
    # google环境
    headers = {
        'name': '',
        # "domain": "www.walmart.com",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'sec-ch-ua': f'"Chromium";v="{v1}", "Not:A-Brand";v="{v2}", "Brave";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36"
    }
    return headers


def create_driver(chrome_path=None, proxies=None):
    """创建一个配置了代理的Selenium WebDriver"""
    if chrome_path:
        service = Service(executable_path=chrome_path)
    else:
        service = Service()  # 根据实际情况设置路径
    
    options = Options()
    # 不再使用无头模式，使浏览器可见
    # options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    
    # 设置代理
    if proxies:
        options.add_argument(f'--proxy-server={proxies["http"]}')
    
    # 添加自定义headers
    headers = Headers()
    options.add_argument(f'user-agent={headers["User-Agent"]}')
    options.add_argument(f'accept={headers["Accept"]}')
    options.add_argument(f'accept-language={headers["Accept-Language"]}')
    
    driver = webdriver.Chrome(service=service, options=options)
    return driver


def _pxvid_get():
    """通过HEAD请求获取Walmart令牌"""
    print("尝试通过HEAD请求获取令牌...")
    url = f"https://www.walmart.com/ip/{random.randint(1, 10000)}{str(time.time())}{str(random.randint(1, 10000))}{str(time.time())}{str(random.randint(1, 10000))}"
    for attempt in range(3):
        try:
            response = requests.head(url, headers=Headers(), proxies=get_ip_address(), verify=False, timeout=10)
            
            # 尝试从cookies中获取_pxhd
            if '_pxhd' in response.cookies:
                _pxvid = response.cookies.get('_pxhd').split(':')[-1]
                print(f"成功获取令牌: {_pxvid[:15]}...")
                return _pxvid
            
            # 检查其他可能的令牌
            for cookie_name in ['_pxvid', '_px3']:
                if cookie_name in response.cookies:
                    token = response.cookies.get(cookie_name)
                    print(f"成功获取 {cookie_name} 令牌: {token[:15]}...")
                    return token
            
            print(f"尝试 {attempt+1}: 未能找到令牌, 重试...")
        except Exception as e:
            print(f"尝试 {attempt+1}: 获取令牌时出错: {str(e)}")
            if attempt == 2:
                print("多次尝试后仍无法获取令牌")
                return None
    return None


def process_id(id, use_auto_notify=False, skip_check=False, chrome_path=None, proxies=None, retry_count=2):
    """进入Walmart队列，并在可用时提醒用户"""
    print(f"处理 ID: {id}")
    
    # 首先获取令牌
    token = None
    max_token_attempts = 3
    for token_attempt in range(max_token_attempts):
        print(f"获取Walmart令牌，尝试 {token_attempt+1}/{max_token_attempts}")
        token = _pxvid_get()
        if token:
            print(f"获取令牌成功: {token[:15]}...")
            break
        else:
            print(f"获取令牌失败，等待10秒后重试...")
            time.sleep(10)
    
    if not token:
        print("多次尝试后仍无法获取令牌，将尝试继续执行...")
    
    queue_driver = None
    success = False
    
    try:
        for attempt in range(retry_count + 1):
            try:
                print(f"处理ID {id} - 尝试 {attempt + 1}/{retry_count + 1}")
                queue_driver = create_driver(chrome_path=chrome_path, proxies=proxies)
                
                # 准备cookie参数
                cookies_to_try = []
                if token:
                    cookies_to_try = [
                        {"name": "_pxhd", "value": token, "domain": ".walmart.com", "path": "/"},
                        {"name": "_pxvid", "value": token, "domain": ".walmart.com", "path": "/"},
                        {"name": "_px3", "value": token, "domain": ".walmart.com", "path": "/"}
                    ]
                
                # 直接访问产品页面
                product_url = f"https://www.walmart.com/ip/{id}"
                print(f"直接访问产品页面: {product_url}")
                queue_driver.get(product_url)
                
                # 尝试快速设置cookie（注意：这可能不会成功，因为页面已经开始加载）
                if token and cookies_to_try:
                    try:
                        print("尝试设置令牌cookie...")
                        for cookie_data in cookies_to_try:
                            try:
                                queue_driver.add_cookie(cookie_data)
                                print(f"设置 {cookie_data['name']} cookie 成功")
                            except Exception as e:
                                print(f"设置 {cookie_data['name']} cookie 失败: {str(e)}")
                        
                        # 刷新页面以应用cookie
                        print("刷新页面以应用cookie...")
                        queue_driver.refresh()
                    except Exception as e:
                        print(f"设置cookie过程中出错: {str(e)}")
                
                # 等待页面加载完成
                print("等待页面加载完成...")
                time.sleep(15)
                
                # 保存屏幕截图用于调试
                try:
                    screenshot_file = f"screenshot_{id}_{attempt}.png"
                    queue_driver.save_screenshot(screenshot_file)
                    print(f"已保存页面截图至 {screenshot_file}")
                except Exception as e:
                    print(f"保存截图失败: {str(e)}")
                
                current_url = queue_driver.current_url
                page_source = queue_driver.page_source
                
                print(f"当前URL: {current_url}")
                
                # 检查是否在队列中
                if "queue-it" in current_url:
                    print("检测到队列...")
                    queue_wait_handler(queue_driver, id, use_auto_notify)
                    success = True
                    break
                
                # 检查是否有"CAPTCHA"或"human verification"
                if "captcha" in page_source.lower() or "human verification" in page_source.lower():
                    print("检测到验证码或人机验证...")
                    queue_wait_handler(queue_driver, id, use_auto_notify)
                    success = True
                    break
                
                # 检查是否已直接跳转到商品页面
                if "Add to cart" in page_source or "加入购物车" in page_source:
                    print("直接进入商品页面 - 无需排队!")
                    notification_handler(id, "No Queue", use_auto_notify)
                    success = True
                    break
                
                print(f"尝试 {attempt + 1} 未检测到队列或直接进入商品页面，将重试...")
                
            except Exception as e:
                print(f"尝试 {attempt + 1} 出错: {str(e)}")
                traceback.print_exc()
            
            finally:
                # 确保每次尝试后关闭浏览器
                if queue_driver:
                    try:
                        queue_driver.quit()
                        print(f"尝试 {attempt + 1} 的浏览器已关闭")
                    except:
                        pass
                
                # 如果没有成功且还有更多尝试，等待一段时间
                if not success and attempt < retry_count:
                    wait_time = 15 + (5 * attempt)  # 根据尝试次数增加等待时间
                    print(f"等待 {wait_time} 秒后进行下一次尝试...")
                    time.sleep(wait_time)
        
        if not success:
            print(f"处理ID {id} 失败，已尝试 {retry_count + 1} 次")
    
    except Exception as e:
        print(f"处理ID {id} 时出现未捕获异常: {str(e)}")
        traceback.print_exc()
    
    finally:
        # 确保最终关闭浏览器
        if queue_driver:
            try:
                queue_driver.quit()
                print("最终清理 - 浏览器已关闭")
            except:
                pass
    
    return success


def request_data():
    print("开始获取ID列表...")
    ids = get_id()
    print(f"总共需要处理 {len(ids)} 个ID")
    all_results = []

    # 使用单线程处理
    max_workers = 1
    print(f"使用 {max_workers} 个线程处理，浏览器将可见")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_id, id) for id in ids]
        for i, future in enumerate(concurrent.futures.as_completed(futures)):
            result = future.result()
            all_results.append(result)
            progress = ((i + 1) / len(ids)) * 100
            print(f"进度: {progress:.2f}% - 已完成ID {result['ID']} 的数据采集")

    # 动态生成列（产品信息列 + 图片列）
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs', 'yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    print("所有数据采集完成，准备保存到Excel...")
    df = pd.DataFrame(all_results).reindex(columns=columns)
    output_file = 'combined_results-usItemIds410-2.xlsx'
    df.to_excel(output_file, index=False)
    print(f"结果已保存到 {output_file}")


def notification_handler(id, message, use_auto_notify=False):
    """处理通知逻辑"""
    print(f"\n===== 商品 {id} 就绪! =====")
    print(f"状态: {message}")
    print(f"链接: https://www.walmart.com/ip/{id}")
    
    # 播放声音提醒
    try:
        # 尝试播放音频提醒
        print("\a")  # 系统提示音
        
        # 如果安装了playsound，使用它播放更明显的提示音
        try:
            from playsound import playsound
            sound_file = "alert.mp3"  # 确保此文件存在
            playsound(sound_file)
            print(f"已播放提示音: {sound_file}")
        except:
            print("未能播放自定义提示音，使用系统提示音代替")
    except:
        print("无法播放提示音")
    
    # 自动通知功能（可根据需要扩展）
    if use_auto_notify:
        try:
            # 这里可以添加更多的通知方式，如发送邮件、短信等
            print("尝试发送自动通知...")
            # 示例: send_email(f"Walmart商品 {id} 就绪!", f"状态: {message}\n链接: https://www.walmart.com/ip/{id}")
        except Exception as e:
            print(f"发送自动通知失败: {str(e)}")


def queue_wait_handler(driver, id, use_auto_notify=False):
    """处理队列等待逻辑"""
    print(f"\n===== 检测到 Walmart 队列 - ID: {id} =====")
    print("正在等待队列...")
    
    max_wait_time = 3600  # 最长等待时间（秒）
    check_interval = 10    # 检查间隔（秒）
    total_wait_time = 0
    
    try:
        while total_wait_time < max_wait_time:
            # 保存队列页面截图
            try:
                screenshot_file = f"queue_{id}_{total_wait_time}.png"
                driver.save_screenshot(screenshot_file)
                print(f"已保存队列截图至 {screenshot_file}")
            except:
                pass
            
            # 获取当前页面信息
            current_url = driver.current_url
            page_source = driver.page_source
            
            # 检查是否已经通过队列
            if "queue-it" not in current_url:
                print("已通过队列!")
                
                # 检查是否到达商品页面
                if "Add to cart" in page_source or "加入购物车" in page_source:
                    notification_handler(id, "Queue Completed - Product Available", use_auto_notify)
                    return True
                else:
                    print("通过队列但未到达商品页面，可能是其他页面或错误")
                    notification_handler(id, "Queue Completed - Unknown Page", use_auto_notify)
                    return False
            
            # 尝试从队列页面获取等待时间
            wait_text = ""
            try:
                if "预计等待时间" in page_source:
                    wait_text = re.search(r'预计等待时间[:：]\s*([^<]+)', page_source)
                elif "estimated wait time" in page_source.lower():
                    wait_text = re.search(r'estimated wait time[:：]?\s*([^<]+)', page_source.lower())
                
                if wait_text:
                    print(f"队列估计等待时间: {wait_text.group(1).strip()}")
            except:
                pass
            
            # 等待一段时间后再次检查
            print(f"已等待 {total_wait_time} 秒，继续等待...")
            time.sleep(check_interval)
            total_wait_time += check_interval
        
        # 如果超过最大等待时间
        print(f"等待队列超时 ({max_wait_time} 秒)")
        notification_handler(id, "Queue Timeout", use_auto_notify)
        return False
        
    except Exception as e:
        print(f"队列等待过程中出错: {str(e)}")
        traceback.print_exc()
        notification_handler(id, f"Queue Error: {str(e)}", use_auto_notify)
        return False


if __name__ == '__main__':
    print("="*50)
    print("Walmart商品信息抓取程序 - 可见浏览器模式")
    print("="*50)
    
    # 直接开始抓取
    print("开始使用代理进行数据抓取...")
    proxy_info = get_ip_address()
    print(f"使用代理: {proxy_info['http']}")
    
    time1 = time.time()
    request_data()
    elapsed_time = time.time() - time1
    
    # 格式化用时，显示为小时:分钟:秒
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("="*50)
    print(f"总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    print("="*50)
