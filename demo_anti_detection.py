# coding: utf-8
# 反检测演示脚本

import importlib.util
import logging
import time
import requests

# 动态导入1.py模块
spec = importlib.util.spec_from_file_location("main_module", "1.py")
main_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(main_module)

# 导入需要的类和函数
ProxyManager = main_module.ProxyManager
AntiDetectionManager = main_module.AntiDetectionManager
trigger_anti_detection = main_module.trigger_anti_detection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def demo_step_by_step():
    """分步演示反检测功能"""
    print("="*60)
    print("🚀 Walmart反爬虫检测与绕过演示")
    print("="*60)
    
    # 步骤1: 初始化
    print("\n📋 步骤1: 初始化代理池和反检测管理器")
    proxy_manager = ProxyManager()
    anti_detection_manager = AntiDetectionManager()
    
    stats = proxy_manager.get_stats()
    print(f"✅ 代理池加载完成: {stats['total']} 个代理")
    
    # 步骤2: 故意触发反检测
    print("\n🎯 步骤2: 故意触发反检测机制")
    print("使用明显的爬虫特征发送请求...")
    
    # 使用bot headers快速请求
    bot_headers = {
        "User-Agent": "Python-requests/2.28.1",
        "Accept": "*/*"
    }
    
    test_url = "https://www.walmart.com/ip/123456789"
    print(f"目标URL: {test_url}")
    
    try:
        response = requests.get(test_url, headers=bot_headers, timeout=10)
        is_detected, reason = anti_detection_manager.detect_anti_crawler(response)
        
        if is_detected:
            print(f"🔴 成功触发反检测! 检测原因: {reason}")
            print(f"   状态码: {response.status_code}")
        else:
            print(f"🟡 未触发反检测，状态码: {response.status_code}")
    except Exception as e:
        print(f"🔴 请求异常（可能被拦截）: {str(e)}")
    
    # 步骤3: 使用反检测绕过技术
    print("\n🛡️ 步骤3: 使用反检测绕过技术")
    
    # 获取代理和令牌
    proxy = proxy_manager.get_random_proxy()
    token = anti_detection_manager.get_pxvid_token(proxy_manager)
    
    if proxy:
        print(f"✅ 获取代理: {proxy['info']['host']}:{proxy['info']['port']}")
    if token:
        print(f"✅ 获取令牌: {token[:15]}...")
    
    # 使用高级技术重新请求
    print("\n🔄 使用代理+令牌+随机Headers重新请求...")
    
    headers = anti_detection_manager.generate_headers()
    cookies = {"_pxvid": token} if token else {}
    
    try:
        if proxy:
            response = requests.get(
                test_url,
                headers=headers,
                cookies=cookies,
                proxies={'http': proxy['http'], 'https': proxy['https']},
                timeout=15,
                verify=False
            )
        else:
            response = requests.get(test_url, headers=headers, cookies=cookies, timeout=15)
        
        is_detected, reason = anti_detection_manager.detect_anti_crawler(response)
        
        print(f"📊 请求结果:")
        print(f"   状态码: {response.status_code}")
        print(f"   反检测状态: {'🔴 检测到' if is_detected else '🟢 绕过成功'}")
        print(f"   检测原因: {reason}")
        
        if not is_detected and response.status_code == 200:
            print("🎉 成功绕过反检测机制!")
            
            # 检查是否包含产品数据
            if '"productName"' in response.text or '"Brand"' in response.text:
                print("✅ 响应包含产品数据")
            else:
                print("⚠️ 响应不包含产品数据（可能是其他页面）")
        
    except Exception as e:
        print(f"🔴 请求失败: {str(e)}")
    
    # 步骤4: 持续测试演示
    print("\n🔄 步骤4: 持续测试演示（最多3次）")
    
    for attempt in range(3):
        print(f"\n--- 尝试 {attempt + 1} ---")
        
        # 获取新的代理和令牌
        proxy = proxy_manager.get_random_proxy()
        token = anti_detection_manager.get_pxvid_token(proxy_manager)
        
        if not proxy:
            print("❌ 无可用代理")
            continue
        
        headers = anti_detection_manager.generate_headers()
        cookies = {"_pxvid": token} if token else {}
        
        try:
            response = requests.get(
                test_url,
                headers=headers,
                cookies=cookies,
                proxies={'http': proxy['http'], 'https': proxy['https']},
                timeout=10,
                verify=False
            )
            
            is_detected, reason = anti_detection_manager.detect_anti_crawler(response)
            
            print(f"代理: {proxy['info']['host']}:{proxy['info']['port']}")
            print(f"状态: {'🔴 被检测' if is_detected else '🟢 成功'} - {reason}")
            
            if not is_detected:
                print("🎉 绕过成功，停止测试")
                break
            else:
                proxy_manager.mark_proxy_failed(proxy)
                print("⚠️ 标记代理失效，继续尝试...")
                
        except Exception as e:
            print(f"🔴 请求异常: {str(e)}")
            proxy_manager.mark_proxy_failed(proxy)
        
        time.sleep(2)  # 短暂延时
    
    # 最终统计
    final_stats = proxy_manager.get_stats()
    print(f"\n📈 最终统计:")
    print(f"   代理池状态: {final_stats['available']}/{final_stats['total']} 可用")
    print(f"   失效代理: {final_stats['failed']} 个")
    
    print("\n" + "="*60)
    print("🏁 演示完成!")
    print("="*60)

def quick_test():
    """快速测试"""
    print("🚀 快速反检测测试")
    print("-" * 30)
    
    proxy_manager = ProxyManager()
    anti_detection_manager = AntiDetectionManager()
    
    # 触发反检测
    result = trigger_anti_detection(proxy_manager, anti_detection_manager)
    print(f"触发反检测: {'✅ 成功' if result else '❌ 失败'}")
    
    # 测试绕过
    proxy = proxy_manager.get_random_proxy()
    token = anti_detection_manager.get_pxvid_token(proxy_manager)
    
    if proxy and token:
        print("✅ 代理和令牌获取成功")
        print("🎉 反检测系统运行正常")
    else:
        print("❌ 代理或令牌获取失败")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'quick':
        quick_test()
    else:
        demo_step_by_step()
