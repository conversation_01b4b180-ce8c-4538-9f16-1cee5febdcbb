# coding: utf-8
# 高级反爬虫绕过系统
# 使用undetected-chromedriver和高级技术绕过Walmart反爬虫

import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, WebDriverException
import time
import random
import json
import logging
import re
from typing import Dict, List, Optional, Tuple
import pandas as pd
from datetime import datetime
import traceback
import os
import requests
from fake_useragent import UserAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AdvancedProxyManager:
    """高级代理管理器"""
    
    def __init__(self, proxy_file='proxies.txt'):
        self.proxy_file = proxy_file
        self.proxies = []
        self.failed_proxies = set()
        self.proxy_performance = {}  # 记录代理性能
        self.load_proxies()
    
    def load_proxies(self):
        """加载代理并测试连通性"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            proxy_blocks = content.strip().split('\n\n')
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if '协议:' in line:
                        proxy_info['protocol'] = line.split('协议:')[1].strip()
                    elif '地址:' in line:
                        proxy_info['host'] = line.split('地址:')[1].strip()
                    elif '端口:' in line:
                        proxy_info['port'] = line.split('端口:')[1].strip()
                    elif '用户名:' in line:
                        proxy_info['username'] = line.split('用户名:')[1].strip()
                    elif '密码:' in line:
                        proxy_info['password'] = line.split('密码:')[1].strip()
                
                if len(proxy_info) == 5:
                    self.proxies.append(proxy_info)
                    # 初始化性能记录
                    proxy_key = f"{proxy_info['host']}:{proxy_info['port']}"
                    self.proxy_performance[proxy_key] = {
                        'success_count': 0,
                        'fail_count': 0,
                        'avg_response_time': 0,
                        'last_used': 0
                    }
            
            logging.info(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            logging.error(f"加载代理文件失败: {str(e)}")
            raise
    
    def get_best_proxy(self):
        """获取性能最好的可用代理"""
        available_proxies = [p for p in self.proxies if self._proxy_key(p) not in self.failed_proxies]
        
        if not available_proxies:
            logging.warning("所有代理都已失效，重置失效列表")
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        if not available_proxies:
            return None
        
        # 根据性能排序选择最佳代理
        def proxy_score(proxy):
            key = self._proxy_key(proxy)
            perf = self.proxy_performance[key]
            success_rate = perf['success_count'] / max(1, perf['success_count'] + perf['fail_count'])
            time_penalty = max(0, time.time() - perf['last_used'] - 300) / 300  # 5分钟冷却奖励
            return success_rate + time_penalty * 0.1
        
        best_proxy = max(available_proxies, key=proxy_score)
        return self.format_proxy_for_chrome(best_proxy)
    
    def format_proxy_for_chrome(self, proxy_info):
        """格式化代理供Chrome使用"""
        if proxy_info['protocol'].lower() == 'socks5':
            proxy_str = f"socks5://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
        else:
            proxy_str = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
        
        return {
            'proxy_string': proxy_str,
            'host': proxy_info['host'],
            'port': proxy_info['port'],
            'username': proxy_info['username'],
            'password': proxy_info['password'],
            'protocol': proxy_info['protocol'],
            'info': proxy_info
        }
    
    def record_proxy_result(self, proxy_info, success: bool, response_time: float = 0):
        """记录代理使用结果"""
        if not proxy_info or 'info' not in proxy_info:
            return
        
        key = self._proxy_key(proxy_info['info'])
        if key in self.proxy_performance:
            perf = self.proxy_performance[key]
            if success:
                perf['success_count'] += 1
                if response_time > 0:
                    # 更新平均响应时间
                    total_time = perf['avg_response_time'] * (perf['success_count'] - 1) + response_time
                    perf['avg_response_time'] = total_time / perf['success_count']
            else:
                perf['fail_count'] += 1
            perf['last_used'] = time.time()
    
    def mark_proxy_failed(self, proxy_info):
        """标记代理为失效"""
        if proxy_info and 'info' in proxy_info:
            proxy_key = self._proxy_key(proxy_info['info'])
            self.failed_proxies.add(proxy_key)
            logging.warning(f"标记代理失效: {proxy_info['info']['host']}:{proxy_info['info']['port']}")
    
    def _proxy_key(self, proxy_info):
        """生成代理的唯一标识"""
        return f"{proxy_info['host']}:{proxy_info['port']}"

    def get_stats(self):
        """获取代理池统计信息"""
        available_proxies = len([p for p in self.proxies
                               if self._proxy_key(p) not in self.failed_proxies])

        return {
            'total': len(self.proxies),
            'available': available_proxies,
            'failed': len(self.failed_proxies)
        }


class UndetectedBrowserManager:
    """不被检测的浏览器管理器"""
    
    def __init__(self, proxy_manager: AdvancedProxyManager):
        self.proxy_manager = proxy_manager
        self.driver = None
        self.current_proxy = None
        self.ua = UserAgent()
    
    def create_driver(self, use_proxy=True, headless=False):
        """创建不被检测的Chrome浏览器"""
        try:
            # 获取代理
            if use_proxy:
                self.current_proxy = self.proxy_manager.get_best_proxy()
                if not self.current_proxy:
                    logging.error("无可用代理")
                    return None
            
            # Chrome选项配置
            options = uc.ChromeOptions()
            
            # 基础反检测配置
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')  # 禁用图片加载提高速度
            options.add_argument('--disable-javascript')  # 可选：禁用JS
            
            # 高级反检测配置
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 随机窗口大小
            width = random.randint(1200, 1920)
            height = random.randint(800, 1080)
            options.add_argument(f'--window-size={width},{height}')
            
            # 代理配置
            if use_proxy and self.current_proxy:
                if self.current_proxy['protocol'].lower() == 'socks5':
                    options.add_argument(f'--proxy-server=socks5://{self.current_proxy["host"]}:{self.current_proxy["port"]}')
                else:
                    options.add_argument(f'--proxy-server=http://{self.current_proxy["host"]}:{self.current_proxy["port"]}')
            
            # 随机User-Agent
            user_agent = self.ua.random
            options.add_argument(f'--user-agent={user_agent}')
            
            if headless:
                options.add_argument('--headless')
            
            # 创建驱动 - 使用系统Chrome避免下载问题
            try:
                self.driver = uc.Chrome(options=options, version_main=None, driver_executable_path=None)
            except Exception as e:
                logging.warning(f"使用默认Chrome失败，尝试指定路径: {str(e)}")
                # 尝试常见的Chrome路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
                ]

                for chrome_path in chrome_paths:
                    if os.path.exists(chrome_path):
                        options.binary_location = chrome_path
                        break

                self.driver = uc.Chrome(options=options, version_main=None)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": user_agent,
                "acceptLanguage": "en-US,en;q=0.9",
                "platform": "Win32"
            })
            
            logging.info(f"浏览器创建成功，代理: {self.current_proxy['host'] if self.current_proxy else 'None'}")
            return self.driver
            
        except Exception as e:
            logging.error(f"创建浏览器失败: {str(e)}")
            return None
    
    def close_driver(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("浏览器已关闭")
            except:
                pass
            finally:
                self.driver = None


class HumanBehaviorSimulator:
    """人类行为模拟器"""
    
    def __init__(self, driver):
        self.driver = driver
        self.actions = ActionChains(driver)
    
    def random_delay(self, min_seconds=1, max_seconds=5):
        """随机延时"""
        delay = random.uniform(min_seconds, max_seconds)
        logging.debug(f"随机延时: {delay:.2f}秒")
        time.sleep(delay)
    
    def simulate_human_typing(self, element, text, typing_speed=0.1):
        """模拟人类打字"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, typing_speed))
    
    def simulate_mouse_movement(self):
        """模拟鼠标移动"""
        try:
            # 获取页面尺寸
            size = self.driver.get_window_size()
            width, height = size['width'], size['height']
            
            # 随机移动鼠标
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, width - 100)
                y = random.randint(100, height - 100)
                self.actions.move_by_offset(x, y).perform()
                time.sleep(random.uniform(0.5, 1.5))
                
        except Exception as e:
            logging.debug(f"鼠标移动模拟失败: {str(e)}")
    
    def simulate_scrolling(self):
        """模拟页面滚动"""
        try:
            # 随机滚动
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(200, 800)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(1, 2))
                
            # 滚动回顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(0.5, 1))
            
        except Exception as e:
            logging.debug(f"滚动模拟失败: {str(e)}")
    
    def simulate_page_interaction(self):
        """模拟页面交互"""
        self.random_delay(2, 4)
        self.simulate_mouse_movement()
        self.random_delay(1, 2)
        self.simulate_scrolling()
        self.random_delay(1, 3)


class WalmartAntiDetectionHandler:
    """Walmart专用反检测处理器"""

    def __init__(self, browser_manager: UndetectedBrowserManager, proxy_manager: AdvancedProxyManager):
        self.browser_manager = browser_manager
        self.proxy_manager = proxy_manager
        self.driver = None
        self.behavior_simulator = None
        self.session_cookies = {}

    def initialize_session(self):
        """初始化会话"""
        try:
            self.driver = self.browser_manager.create_driver(use_proxy=True, headless=False)
            if not self.driver:
                return False

            self.behavior_simulator = HumanBehaviorSimulator(self.driver)

            # 首先访问主页建立会话
            logging.info("访问Walmart主页建立会话...")
            self.driver.get("https://www.walmart.com")

            # 模拟人类行为
            self.behavior_simulator.simulate_page_interaction()

            # 保存初始cookies
            self.session_cookies = {cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()}
            logging.info(f"会话初始化成功，获得 {len(self.session_cookies)} 个cookies")

            return True

        except Exception as e:
            logging.error(f"会话初始化失败: {str(e)}")
            return False

    def detect_anti_crawler_response(self, current_url: str, page_source: str) -> Tuple[bool, str]:
        """检测反爬虫响应"""
        # 检查URL中的反爬虫标识
        if any(keyword in current_url.lower() for keyword in ['queue-it', 'captcha', 'blocked', 'access-denied']):
            return True, f"URL包含反爬虫标识: {current_url}"

        # 检查页面内容
        page_lower = page_source.lower()
        anti_crawler_keywords = [
            'queue-it', 'captcha', 'blocked', 'access denied', 'too many requests',
            'rate limit', 'human verification', 'security check', 'unusual traffic',
            'please wait', 'checking your browser', 'cloudflare', 'perimeter x'
        ]

        for keyword in anti_crawler_keywords:
            if keyword in page_lower:
                return True, f"页面包含反爬虫关键词: {keyword}"

        # 检查是否是空白页面或错误页面
        if len(page_source.strip()) < 1000:
            return True, "页面内容过少，可能被拦截"

        # 检查是否包含正常的商品页面元素
        if '"productName"' not in page_source and '"Brand"' not in page_source:
            if 'walmart.com' in current_url and '/ip/' in current_url:
                return True, "商品页面缺少必要元素"

        return False, "正常响应"

    def handle_queue_system(self, max_wait_minutes=30):
        """处理队列系统"""
        logging.info("检测到队列系统，开始等待...")

        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        check_interval = 30  # 30秒检查一次

        while time.time() - start_time < max_wait_seconds:
            try:
                current_url = self.driver.current_url
                page_source = self.driver.page_source

                # 保存截图用于调试
                screenshot_path = f"queue_screenshot_{int(time.time())}.png"
                self.driver.save_screenshot(screenshot_path)
                logging.info(f"队列截图已保存: {screenshot_path}")

                # 检查是否还在队列中
                if 'queue-it' not in current_url.lower():
                    logging.info("已通过队列系统!")
                    return True

                # 模拟人类在等待时的行为
                self.behavior_simulator.random_delay(10, 20)

                # 尝试刷新页面（模拟用户行为）
                if random.random() < 0.3:  # 30%概率刷新
                    logging.info("模拟用户刷新页面...")
                    self.driver.refresh()
                    time.sleep(random.uniform(5, 10))

                elapsed_minutes = (time.time() - start_time) / 60
                logging.info(f"队列等待中... 已等待 {elapsed_minutes:.1f} 分钟")

                time.sleep(check_interval)

            except Exception as e:
                logging.error(f"队列处理过程中出错: {str(e)}")
                return False

        logging.warning(f"队列等待超时 ({max_wait_minutes} 分钟)")
        return False

    def handle_captcha(self):
        """处理验证码（需要人工干预）"""
        logging.warning("检测到验证码，需要人工处理...")

        # 保存截图
        screenshot_path = f"captcha_screenshot_{int(time.time())}.png"
        self.driver.save_screenshot(screenshot_path)
        logging.info(f"验证码截图已保存: {screenshot_path}")

        # 等待用户手动处理验证码
        input("请手动完成验证码，然后按Enter继续...")

        # 检查是否通过验证码
        time.sleep(3)
        current_url = self.driver.current_url
        page_source = self.driver.page_source

        is_detected, reason = self.detect_anti_crawler_response(current_url, page_source)
        return not is_detected

    def smart_page_load(self, url: str, max_retries=3) -> Tuple[bool, str]:
        """智能页面加载"""
        for attempt in range(max_retries):
            try:
                logging.info(f"尝试 {attempt + 1}: 加载页面 {url}")

                # 随机延时
                self.behavior_simulator.random_delay(3, 8)

                # 加载页面
                self.driver.get(url)

                # 等待页面加载
                WebDriverWait(self.driver, 15).until(
                    lambda driver: driver.execute_script("return document.readyState") == "complete"
                )

                # 模拟人类行为
                self.behavior_simulator.simulate_page_interaction()

                # 获取页面信息
                current_url = self.driver.current_url
                page_source = self.driver.page_source

                # 检测反爬虫
                is_detected, reason = self.detect_anti_crawler_response(current_url, page_source)

                if not is_detected:
                    logging.info(f"页面加载成功: {url}")
                    return True, page_source

                logging.warning(f"尝试 {attempt + 1}: 检测到反爬虫 - {reason}")

                # 处理不同类型的反爬虫
                if 'queue-it' in current_url.lower():
                    if self.handle_queue_system():
                        return True, self.driver.page_source
                elif 'captcha' in reason.lower():
                    if self.handle_captcha():
                        return True, self.driver.page_source

                # 如果检测到反爬虫，切换代理重试
                if attempt < max_retries - 1:
                    logging.info("切换代理重试...")
                    self.proxy_manager.mark_proxy_failed(self.browser_manager.current_proxy)
                    self.browser_manager.close_driver()

                    if not self.initialize_session():
                        logging.error("重新初始化会话失败")
                        continue

            except TimeoutException:
                logging.warning(f"尝试 {attempt + 1}: 页面加载超时")
            except Exception as e:
                logging.error(f"尝试 {attempt + 1}: 页面加载异常 - {str(e)}")

        return False, ""

    def extract_product_data(self, page_source: str, product_id: str) -> Dict:
        """提取商品数据"""
        row_data = {'ID': product_id}

        try:
            html = page_source.replace('\f', '').replace('\n', '').replace('\t', '')

            # 提取基本信息
            row_data['Brand'] = self._extract_field(html, r'"Brand","name":"(.*?)"')
            row_data['Title'] = self._extract_field(html, r'"productName":"(.*?)"')
            row_data['LongDescription'] = self._extract_field(html, r'"longDescription":"(.*?)","shortDescription')
            row_data['ProductDetails'] = self._extract_field(html, r'"shortDescription":"(.*?)","fulfillmentType":')
            row_data['Link'] = self._extract_field(html, r'\{"@type":"Offer","url":"(.*?)",')
            row_data['zy'] = self._extract_field(html, r',"sellerDisplayName":"(.*?)","')
            row_data['price'] = self._extract_field(html, r'"priceCurrency":"USD","price":(.*?),"')
            row_data['left'] = self._extract_field(html, r'"usecase":"SHIPPING","value":"(.*?)",')
            row_data['wfs'] = self._extract_field(html, r'"wfsEnabled":(.*?),')
            row_data['yunfei'] = self._extract_field(html, r'"shipPrice":{"price":(.*?),"')
            row_data['pingfen'] = self._extract_field(html, r'@type":"AggregateRating","ratingValue":(.*?),"')
            row_data['pinglun'] = self._extract_field(html, r'totalReviewsCountAsString":"(.*?)","')

            # 检查商品是否有效
            if row_data['zy'] == '':
                row_data['Brand'] = 'ID失效或缺货'

            # 提取图片链接
            self._extract_images(html, row_data)

            logging.info(f"商品数据提取成功: {product_id}")

        except Exception as e:
            logging.error(f"商品数据提取失败 {product_id}: {str(e)}")
            row_data['Brand'] = '数据提取失败'

        return row_data

    def _extract_field(self, html: str, pattern: str) -> str:
        """提取单个字段"""
        try:
            matches = re.findall(pattern, html)
            return matches[0] if matches else ''
        except:
            return ''

    def _extract_images(self, html: str, row_data: Dict):
        """提取图片链接"""
        try:
            all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
            if all_images_match:
                images_data = json.loads(f'[{all_images_match.group(1)}]')
                for idx, img in enumerate(images_data[:20], 1):
                    if 'url' in img:
                        row_data[f'Image_{idx}'] = img['url']
        except Exception as e:
            logging.debug(f"图片提取失败: {str(e)}")

    def cleanup(self):
        """清理资源"""
        if self.browser_manager:
            self.browser_manager.close_driver()
