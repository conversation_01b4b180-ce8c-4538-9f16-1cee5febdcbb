#!/usr/bin/env python
# coding: utf-8

import requests
import urllib3
from 队列 import get_ip_address

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

proxies = get_ip_address()
print("使用代理配置:")
print("HTTP代理:", proxies['http'])
print("HTTPS代理:", proxies['https'])

try:
    # 尝试通过代理连接到一个测试网站
    print("正在测试代理连接...")
    response = requests.get("https://httpbin.org/ip", proxies=proxies, timeout=10, verify=False)
    print("连接状态码:", response.status_code)
    print("返回内容:", response.text)
    print("连接成功!")
except Exception as e:
    print("连接失败:", str(e)) 