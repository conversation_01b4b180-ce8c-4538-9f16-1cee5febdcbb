# coding: utf-8
# 基于requests的高级反爬虫绕过系统

import requests
import time
import random
import json
import logging
import re
from typing import Dict, List, Optional, Tuple
import pandas as pd
from datetime import datetime
import traceback
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import ssl
from fake_useragent import UserAgent

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('requests_advanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AdvancedRequestsSession:
    """高级requests会话管理器"""
    
    def __init__(self, proxy_manager):
        self.proxy_manager = proxy_manager
        self.session = None
        self.ua = UserAgent()
        self.current_proxy = None
        self.session_cookies = {}
        self.create_session()
    
    def create_session(self):
        """创建高级会话"""
        self.session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 配置SSL
        self.session.verify = False
        
        # 获取代理
        self.current_proxy = self.proxy_manager.get_best_proxy()
        if self.current_proxy:
            self.session.proxies = {
                'http': self.current_proxy['proxy_string'],
                'https': self.current_proxy['proxy_string']
            }
            logging.info(f"使用代理: {self.current_proxy['host']}:{self.current_proxy['port']}")
    
    def get_advanced_headers(self):
        """生成高级请求头"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }
        
        # 随机添加一些可选头部
        optional_headers = {
            'sec-ch-ua': '"Google Chrome";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        
        for key, value in optional_headers.items():
            if random.random() > 0.3:  # 70%概率添加
                headers[key] = value
        
        return headers
    
    def make_request(self, url, method='GET', **kwargs):
        """发送高级请求"""
        headers = self.get_advanced_headers()
        
        # 合并用户提供的headers
        if 'headers' in kwargs:
            headers.update(kwargs['headers'])
        kwargs['headers'] = headers
        
        # 添加cookies
        if self.session_cookies:
            if 'cookies' not in kwargs:
                kwargs['cookies'] = {}
            kwargs['cookies'].update(self.session_cookies)
        
        # 设置超时
        kwargs.setdefault('timeout', 15)
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # 更新cookies
            for cookie in response.cookies:
                self.session_cookies[cookie.name] = cookie.value
            
            return response
            
        except Exception as e:
            logging.error(f"请求失败: {str(e)}")
            raise


class RequestsBasedAntiDetection:
    """基于requests的反检测系统"""
    
    def __init__(self, proxy_manager):
        self.proxy_manager = proxy_manager
        self.session_manager = AdvancedRequestsSession(proxy_manager)
        self.pxvid_tokens = []
        
    def initialize_session(self):
        """初始化会话"""
        try:
            logging.info("初始化Walmart会话...")
            
            # 首先访问主页
            response = self.session_manager.make_request('https://www.walmart.com')
            
            if response.status_code == 200:
                logging.info("主页访问成功")
                
                # 尝试获取pxvid令牌
                self.get_pxvid_token()
                return True
            else:
                logging.error(f"主页访问失败: {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"会话初始化失败: {str(e)}")
            return False
    
    def get_pxvid_token(self):
        """获取pxvid令牌"""
        try:
            logging.info("尝试获取pxvid令牌...")
            
            # 访问特定页面获取令牌
            token_url = "https://www.walmart.com/api/v1/services/px/px.js"
            
            headers = {
                'Referer': 'https://www.walmart.com/',
                'Accept': '*/*',
            }
            
            response = self.session_manager.make_request(token_url, headers=headers)
            
            if response.status_code == 200:
                # 从响应中提取令牌
                content = response.text
                token_match = re.search(r'_pxvid["\']?\s*[:=]\s*["\']([^"\']+)', content)
                
                if token_match:
                    token = token_match.group(1)
                    self.pxvid_tokens.append(token)
                    logging.info(f"成功获取pxvid令牌: {token[:15]}...")
                    return token
            
            # 如果上面的方法失败，尝试生成一个令牌
            token = self.generate_pxvid_token()
            self.pxvid_tokens.append(token)
            logging.info(f"生成pxvid令牌: {token[:15]}...")
            return token
            
        except Exception as e:
            logging.error(f"获取pxvid令牌失败: {str(e)}")
            return self.generate_pxvid_token()
    
    def generate_pxvid_token(self):
        """生成pxvid令牌"""
        import uuid
        return str(uuid.uuid4())
    
    def detect_anti_crawler(self, response):
        """检测反爬虫响应"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True, f"状态码检测: {response.status_code}"
        
        # 检查响应内容
        content = response.text.lower()
        anti_crawler_keywords = [
            'queue-it', 'captcha', 'blocked', 'access denied', 'too many requests',
            'rate limit', 'human verification', 'security check', 'unusual traffic',
            'please wait', 'checking your browser', 'cloudflare', 'perimeter x',
            'px-captcha', 'challenge', 'robot'
        ]
        
        for keyword in anti_crawler_keywords:
            if keyword in content:
                return True, f"关键词检测: {keyword}"
        
        # 检查响应长度
        if len(content) < 1000:
            return True, "响应内容过短"
        
        # 检查是否包含正常商品页面元素
        if 'walmart.com/ip/' in response.url:
            if '"productName"' not in response.text and '"Brand"' not in response.text:
                return True, "缺少商品页面元素"
        
        return False, "正常响应"
    
    def smart_request(self, url, max_retries=3):
        """智能请求"""
        for attempt in range(max_retries):
            try:
                logging.info(f"尝试 {attempt + 1}: 请求 {url}")
                
                # 随机延时
                delay = random.uniform(3, 8)
                time.sleep(delay)
                
                # 准备cookies
                cookies = {}
                if self.pxvid_tokens:
                    cookies['_pxvid'] = random.choice(self.pxvid_tokens)
                
                # 发送请求
                response = self.session_manager.make_request(url, cookies=cookies)
                
                # 检测反爬虫
                is_detected, reason = self.detect_anti_crawler(response)
                
                if not is_detected:
                    logging.info(f"请求成功: {url}")
                    # 记录代理成功
                    if self.session_manager.current_proxy:
                        self.proxy_manager.record_proxy_result(
                            self.session_manager.current_proxy, True, delay
                        )
                    return True, response
                
                logging.warning(f"尝试 {attempt + 1}: 检测到反爬虫 - {reason}")
                
                # 如果检测到反爬虫，切换策略
                if attempt < max_retries - 1:
                    self.switch_proxy_and_session()
                    
                    # 获取新的令牌
                    self.get_pxvid_token()
                    
                    # 增加延时
                    time.sleep(random.uniform(10, 20))
                
            except Exception as e:
                logging.error(f"尝试 {attempt + 1}: 请求异常 - {str(e)}")
                
                if attempt < max_retries - 1:
                    self.switch_proxy_and_session()
        
        return False, None
    
    def switch_proxy_and_session(self):
        """切换代理和会话"""
        logging.info("切换代理和会话...")
        
        # 标记当前代理失效
        if self.session_manager.current_proxy:
            self.proxy_manager.mark_proxy_failed(self.session_manager.current_proxy)
        
        # 创建新会话
        self.session_manager = AdvancedRequestsSession(self.proxy_manager)
    
    def extract_product_data(self, response, product_id):
        """提取商品数据"""
        row_data = {'ID': product_id}
        
        try:
            html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')
            
            # 提取基本信息
            row_data['Brand'] = self._extract_field(html, r'"Brand","name":"(.*?)"')
            row_data['Title'] = self._extract_field(html, r'"productName":"(.*?)"')
            row_data['LongDescription'] = self._extract_field(html, r'"longDescription":"(.*?)","shortDescription')
            row_data['ProductDetails'] = self._extract_field(html, r'"shortDescription":"(.*?)","fulfillmentType":')
            row_data['Link'] = self._extract_field(html, r'\{"@type":"Offer","url":"(.*?)",')
            row_data['zy'] = self._extract_field(html, r',"sellerDisplayName":"(.*?)","')
            row_data['price'] = self._extract_field(html, r'"priceCurrency":"USD","price":(.*?),"')
            row_data['left'] = self._extract_field(html, r'"usecase":"SHIPPING","value":"(.*?)",')
            row_data['wfs'] = self._extract_field(html, r'"wfsEnabled":(.*?),')
            row_data['yunfei'] = self._extract_field(html, r'"shipPrice":{"price":(.*?),"')
            row_data['pingfen'] = self._extract_field(html, r'@type":"AggregateRating","ratingValue":(.*?),"')
            row_data['pinglun'] = self._extract_field(html, r'totalReviewsCountAsString":"(.*?)","')
            
            # 检查商品是否有效
            if row_data['zy'] == '':
                row_data['Brand'] = 'ID失效或缺货'
            
            logging.info(f"商品数据提取成功: {product_id}")
            
        except Exception as e:
            logging.error(f"商品数据提取失败 {product_id}: {str(e)}")
            row_data['Brand'] = '数据提取失败'
        
        return row_data
    
    def _extract_field(self, html: str, pattern: str) -> str:
        """提取单个字段"""
        try:
            matches = re.findall(pattern, html)
            return matches[0] if matches else ''
        except:
            return ''


# 导入原有的代理管理器
from advanced_anti_detection import AdvancedProxyManager

def test_requests_based_system():
    """测试基于requests的系统"""
    print("🚀 测试基于requests的高级反爬虫系统")
    print("=" * 50)
    
    try:
        # 初始化
        proxy_manager = AdvancedProxyManager()
        anti_detection = RequestsBasedAntiDetection(proxy_manager)
        
        # 初始化会话
        if not anti_detection.initialize_session():
            print("❌ 会话初始化失败")
            return False
        
        print("✅ 会话初始化成功")
        
        # 测试商品页面
        test_ids = ["123456789", "987654321"]
        results = []
        
        for product_id in test_ids:
            print(f"\n🛒 测试商品ID: {product_id}")
            
            url = f"https://www.walmart.com/ip/{product_id}"
            success, response = anti_detection.smart_request(url)
            
            if success:
                print("✅ 请求成功")
                
                # 检测反爬虫
                is_detected, reason = anti_detection.detect_anti_crawler(response)
                print(f"反爬虫检测: {'🔴 检测到' if is_detected else '🟢 未检测到'} - {reason}")
                
                # 提取数据
                product_data = anti_detection.extract_product_data(response, product_id)
                results.append(product_data)
                print(f"商品品牌: {product_data.get('Brand', '无数据')}")
                
            else:
                print("❌ 请求失败")
                results.append({'ID': product_id, 'Brand': '请求失败'})
        
        # 保存结果
        if results:
            df = pd.DataFrame(results)
            df.to_excel('requests_test_results.xlsx', index=False)
            print(f"\n📊 结果已保存到: requests_test_results.xlsx")
        
        # 显示统计
        stats = proxy_manager.get_stats()
        print(f"代理池状态: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    test_requests_based_system()
