# coding: utf-8
# Project:fenggong_imagelink
# File:1.py
# Author:白茶
# Date: 2025/4/3 上午10:00
# IDE:PyCharm


import concurrent
from datetime import datetime

import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
from concurrent.futures import ThreadPoolExecutor
import json
import queue  # 新增导入queue模块

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


name = "能跑多少跑多少1.xlsx"

def get_id():
    df = pd.read_excel(name)
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids

def load_pxvid_queue():
    """从pxvid.xlsx加载pxvid并生成队列，每个vid重复5次"""
    try:
        df = pd.read_excel('令牌.xlsx')  # 确保文件存在且列名正确
        pxvid_list = df['pxvid'].dropna().astype(str).tolist()
        # 每个vid重复5次
        extended_vids = []
        for vid in pxvid_list:
            extended_vids.extend([vid] * 5)
        random.shuffle(extended_vids)  # 打乱顺序
        q = queue.Queue()
        for vid in extended_vids:
            q.put(vid)
        print(f"成功加载{len(extended_vids)}个pxvid到队列")
        return q
    except Exception as e:
        print(f"加载pxvid队列失败: {str(e)}")
        raise

def get_ip_address():
    proxyAddr = "overseas.tunnel.qg.net:15655"
    authKey = "0MQGW8CU"
    password = "99AF4C18800B"
    proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
        "user": authKey,
        "password": password,
        "server": proxyAddr,
    }
    proxies = {
        "http": proxyUrl,
        "https": proxyUrl,
    }
    # print(proxies)
    return proxies


def md5_encrypt(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()


def Headers():
    # 版本号
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'name': '',
        # "domain": "www.walmart.com",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
    }
    return headers


def process_id(id, vid_queue):
    row_data = {'ID': id}
    max_retries = 3

    # 从队列获取pxvid（每个vid最多使用5次）
    try:
        pxvid = vid_queue.get_nowait()
    except queue.Empty:
        row_data['Brand'] = 'No pxvid available'
        print(f"ID {id}: 无可用pxvid")
        return row_data

    for attempt in range(max_retries):
        try:
            url = f"https://www.walmart.com/ip/{id}"
            cookie = {"_pxvid": pxvid}
            time.sleep(random.randint(5, 10))

            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=get_ip_address(),
                timeout=10
            )

            if response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR':
                html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')
                # 提取产品信息
                row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
                row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"',html) else ''
                row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
                row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
                row_data['Link'] = re.findall('\{"@type":"Offer","url":"(.*?)",', html)[0] if re.findall( '\{"@type":"Offer","url":"(.*?)",', html) else ''
                row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","', html)[0] if re.findall(',"sellerDisplayName":"(.*?)","', html) else ''
                row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"', html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"', html) else ''
                row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",', html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",', html) else ''
                row_data['wfs'] = re.findall('"wfsEnabled":(.*?),', html)[0] if re.findall('"wfsEnabled":(.*?),',html) else ''
                row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"', html)[0] if re.findall('"shipPrice":{"price":(.*?),"', html) else ''
                row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html) else ''
                row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","', html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","', html) else ''
                # print(row_data['Link'])
                # print(row_data['price'])
                if row_data['zy'] == '':
                    row_data['Brand'] = 'ID失效或缺货'

                # 提取图片链接
                image_result = {'ID': id}
                all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
                if all_images_match:
                    try:
                        images_data = json.loads(f'[{all_images_match.group(1)}]')
                        for idx, img in enumerate(images_data[:20], 1):
                            if 'url' in img:
                                image_result[f'Image_{idx}'] = img['url']
                    except Exception as e:
                        print(f"图片解析失败 ID: {id} - {str(e)}")
                row_data.update(image_result)
                break  # 请求成功，退出重试循环
            else:
                print(f"ID {id} 状态码 {response.status_code}, 第{attempt + 1}次重试")
                if response.status_code == 404 and attempt == 2:
                    row_data['Brand'] = 'ID失效或缺货'
        except Exception as e:
            print(f"ID {id} 请求异常: {str(e)}，第 {attempt + 1} 次重试")
            if attempt == 2:
                row_data['Brand'] = '请求失败'
    print(row_data)
    return row_data


def request_data():
    ids = get_id()
    all_results = []

    # 加载pxvid队列
    vid_queue = load_pxvid_queue()

    with ThreadPoolExecutor(max_workers=100) as executor:
        # 传递vid_queue到每个线程
        futures = [executor.submit(process_id, id, vid_queue) for id in ids]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            all_results.append(result)
            print(f"已完成ID {result['ID']} 的数据采集")

    # 生成结果文件
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs','yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    df.to_excel(f'result-{datetime.today().month}-{datetime.today().day}-{name}', index=False)
    print(f"结果已保存到 result-{datetime.today().month}-{datetime.today().day}-{name}")


if __name__ == '__main__':
    time1 = time.time()
    request_data()
    print("用时：", time.time() - time1)
