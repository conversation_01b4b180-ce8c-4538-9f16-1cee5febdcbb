# coding: utf-8
# Project:fenggong_imagelink
# File:1.py (Enhanced with Proxy Pool and Anti-Detection)
# Author:白茶
# Date: 2025/4/3 上午10:00
# IDE:PyCharm

import concurrent
from datetime import datetime
import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
from concurrent.futures import ThreadPoolExecutor
import json
import queue
import logging
from typing import List, Dict, Optional
import traceback

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)


name = "能跑多少跑多少1.xlsx"


class ProxyManager:
    """代理池管理器"""

    def __init__(self, proxy_file='proxies.txt'):
        self.proxy_file = proxy_file
        self.proxies = []
        self.failed_proxies = set()
        self.load_proxies()

    def load_proxies(self):
        """从proxies.txt加载代理信息"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析代理信息
            proxy_blocks = content.strip().split('\n\n')
            for block in proxy_blocks:
                if not block.strip():
                    continue

                lines = block.strip().split('\n')
                proxy_info = {}

                for line in lines:
                    if '协议:' in line:
                        proxy_info['protocol'] = line.split('协议:')[1].strip()
                    elif '地址:' in line:
                        proxy_info['host'] = line.split('地址:')[1].strip()
                    elif '端口:' in line:
                        proxy_info['port'] = line.split('端口:')[1].strip()
                    elif '用户名:' in line:
                        proxy_info['username'] = line.split('用户名:')[1].strip()
                    elif '密码:' in line:
                        proxy_info['password'] = line.split('密码:')[1].strip()

                if len(proxy_info) == 5:  # 确保所有字段都存在
                    self.proxies.append(proxy_info)

            logging.info(f"成功加载 {len(self.proxies)} 个代理")

        except Exception as e:
            logging.error(f"加载代理文件失败: {str(e)}")
            raise

    def get_random_proxy(self):
        """获取随机可用代理"""
        available_proxies = [p for p in self.proxies if self._proxy_key(p) not in self.failed_proxies]

        if not available_proxies:
            logging.warning("所有代理都已失效，重置失效列表")
            self.failed_proxies.clear()
            available_proxies = self.proxies

        if not available_proxies:
            logging.error("没有可用的代理")
            return None

        proxy_info = random.choice(available_proxies)
        return self.format_proxy_for_requests(proxy_info)

    def format_proxy_for_requests(self, proxy_info):
        """将代理信息格式化为requests可用的格式"""
        if proxy_info['protocol'].lower() == 'socks5':
            proxy_url = f"socks5://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
        else:
            proxy_url = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"

        return {
            'http': proxy_url,
            'https': proxy_url,
            'info': proxy_info
        }

    def mark_proxy_failed(self, proxy_info):
        """标记代理为失效"""
        if proxy_info and 'info' in proxy_info:
            proxy_key = self._proxy_key(proxy_info['info'])
            self.failed_proxies.add(proxy_key)
            logging.warning(f"标记代理失效: {proxy_info['info']['host']}:{proxy_info['info']['port']}")

    def _proxy_key(self, proxy_info):
        """生成代理的唯一标识"""
        return f"{proxy_info['host']}:{proxy_info['port']}"

    def get_stats(self):
        """获取代理池统计信息"""
        total = len(self.proxies)
        failed = len(self.failed_proxies)
        available = total - failed
        return {
            'total': total,
            'available': available,
            'failed': failed
        }


class AntiDetectionManager:
    """反检测管理器"""

    def __init__(self):
        self.pxvid_tokens = queue.Queue()
        self.last_token_time = 0
        self.detection_keywords = [
            'queue-it', 'captcha', 'blocked', 'access denied',
            'too many requests', 'rate limit', 'human verification',
            'security check', 'unusual traffic'
        ]

    def get_pxvid_token(self, proxy_manager):
        """获取Walmart令牌"""
        try:
            # 如果队列中有令牌且不超过5分钟，直接使用
            if not self.pxvid_tokens.empty() and (time.time() - self.last_token_time) < 300:
                try:
                    return self.pxvid_tokens.get_nowait()
                except queue.Empty:
                    pass

            # 获取新令牌
            logging.info("尝试获取新的pxvid令牌...")
            proxy_info = proxy_manager.get_random_proxy()

            # 使用随机URL避免缓存
            random_id = random.randint(100000, 999999)
            url = f"https://www.walmart.com/ip/{random_id}"

            headers = self.generate_headers()

            for attempt in range(3):
                try:
                    if proxy_info:
                        response = requests.head(
                            url,
                            headers=headers,
                            proxies={'http': proxy_info['http'], 'https': proxy_info['https']},
                            verify=False,
                            timeout=10
                        )
                    else:
                        response = requests.head(url, headers=headers, verify=False, timeout=10)

                    # 尝试从cookies中获取令牌
                    for cookie_name in ['_pxhd', '_pxvid', '_px3']:
                        if cookie_name in response.cookies:
                            token = response.cookies.get(cookie_name)
                            if ':' in token and cookie_name == '_pxhd':
                                token = token.split(':')[-1]

                            logging.info(f"成功获取 {cookie_name} 令牌: {token[:15]}...")
                            self.pxvid_tokens.put(token)
                            self.last_token_time = time.time()
                            return token

                    logging.warning(f"尝试 {attempt+1}: 未能找到令牌")

                except Exception as e:
                    logging.error(f"尝试 {attempt+1}: 获取令牌时出错: {str(e)}")
                    if proxy_info:
                        proxy_manager.mark_proxy_failed(proxy_info)
                        proxy_info = proxy_manager.get_random_proxy()

                time.sleep(2)

            logging.warning("多次尝试后仍无法获取令牌")
            return None

        except Exception as e:
            logging.error(f"获取令牌过程中出现异常: {str(e)}")
            return None

    def generate_headers(self):
        """生成随机化的请求头"""
        v1 = random.randint(100, 135)
        v2 = random.randint(10, 25)
        v3 = random.randint(400, 600)

        user_agents = [
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36",
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
            f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36"
        ]

        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": random.choice([
                "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
                "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7"
            ]),
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Ch-Ua-Platform": "\"Windows\"",
            "Sec-Fetch-User": "?1",
            "Priority": "u=0, i",
            'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
            f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
            "User-Agent": random.choice(user_agents),
            "Cache-Control": random.choice(["no-cache", "max-age=0", "no-store"]),
            "DNT": "1"
        }

        return headers

    def detect_anti_crawler(self, response):
        """检测反爬虫响应"""
        if not response:
            return True, "No response"

        # 检查状态码
        if response.status_code in [403, 429, 503, 520, 521, 522, 523, 524]:
            return True, f"Status code: {response.status_code}"

        # 检查响应内容
        content = response.text.lower()
        for keyword in self.detection_keywords:
            if keyword in content:
                return True, f"Keyword detected: {keyword}"

        # 检查响应头
        if 'cf-ray' in response.headers and response.status_code != 200:
            return True, "Cloudflare protection"

        # 检查响应时间（异常快的响应可能是拦截）
        if hasattr(response, 'elapsed') and response.elapsed.total_seconds() < 0.1:
            return True, "Response too fast"

        return False, "Normal response"

def get_id():
    df = pd.read_excel(name)
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids

def load_pxvid_queue():
    """从pxvid.xlsx加载pxvid并生成队列，每个vid重复5次"""
    try:
        df = pd.read_excel('令牌.xlsx')  # 确保文件存在且列名正确
        pxvid_list = df['pxvid'].dropna().astype(str).tolist()
        # 每个vid重复5次
        extended_vids = []
        for vid in pxvid_list:
            extended_vids.extend([vid] * 5)
        random.shuffle(extended_vids)  # 打乱顺序
        q = queue.Queue()
        for vid in extended_vids:
            q.put(vid)
        print(f"成功加载{len(extended_vids)}个pxvid到队列")
        return q
    except Exception as e:
        print(f"加载pxvid队列失败: {str(e)}")
        raise

def get_ip_address():
    """保留原有函数作为备用代理"""
    proxyAddr = "overseas.tunnel.qg.net:15655"
    authKey = "0MQGW8CU"
    password = "99AF4C18800B"
    proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
        "user": authKey,
        "password": password,
        "server": proxyAddr,
    }
    proxies = {
        "http": proxyUrl,
        "https": proxyUrl,
    }
    return proxies


def trigger_anti_detection(proxy_manager, anti_detection_manager):
    """故意触发反检测机制"""
    logging.info("开始触发反检测机制...")

    test_urls = [
        "https://www.walmart.com/ip/123456789",
        "https://www.walmart.com/ip/987654321",
        "https://www.walmart.com/ip/555666777"
    ]

    # 使用明显的爬虫特征
    bot_headers = {
        "User-Agent": "Python-requests/2.28.1",
        "Accept": "*/*",
        "Connection": "keep-alive"
    }

    for i in range(10):  # 快速连续请求
        try:
            url = random.choice(test_urls)
            logging.info(f"触发请求 {i+1}: {url}")

            # 不使用代理，使用明显的bot headers
            response = requests.get(url, headers=bot_headers, timeout=5)

            is_detected, reason = anti_detection_manager.detect_anti_crawler(response)
            if is_detected:
                logging.info(f"成功触发反检测! 原因: {reason}")
                return True

            time.sleep(0.1)  # 很短的间隔

        except Exception as e:
            logging.info(f"触发请求异常: {str(e)}")

    logging.warning("未能成功触发反检测，继续执行...")
    return False


def md5_encrypt(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()


def Headers():
    """保留原有Headers函数作为备用"""
    # 版本号
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'name': '',
        # "domain": "www.walmart.com",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
    }
    return headers


def process_id_enhanced(id, vid_queue, proxy_manager, anti_detection_manager):
    """增强版的ID处理函数，集成代理池和反爬虫功能"""
    row_data = {'ID': id}
    max_retries = 5

    logging.info(f"开始处理ID: {id}")

    # 从队列获取pxvid
    pxvid = None
    try:
        pxvid = vid_queue.get_nowait()
        logging.info(f"ID {id}: 使用队列中的pxvid: {pxvid[:15]}...")
    except queue.Empty:
        logging.warning(f"ID {id}: 队列中无可用pxvid，尝试获取新令牌")
        pxvid = anti_detection_manager.get_pxvid_token(proxy_manager)

    if not pxvid:
        logging.warning(f"ID {id}: 无法获取pxvid令牌")

    current_proxy = None

    for attempt in range(max_retries):
        try:
            # 获取代理
            if not current_proxy:
                current_proxy = proxy_manager.get_random_proxy()

            if not current_proxy:
                logging.error(f"ID {id}: 无可用代理")
                row_data['Brand'] = '无可用代理'
                break

            url = f"https://www.walmart.com/ip/{id}"

            # 准备cookies和headers
            cookies = {}
            if pxvid:
                cookies["_pxvid"] = pxvid

            headers = anti_detection_manager.generate_headers()

            # 随机延时
            delay = random.uniform(3, 8)
            logging.info(f"ID {id} 尝试 {attempt + 1}: 延时 {delay:.2f}s")
            time.sleep(delay)

            # 发送请求
            proxies_dict = {'http': current_proxy['http'], 'https': current_proxy['https']}

            response = requests.get(
                url,
                cookies=cookies,
                headers=headers,
                proxies=proxies_dict,
                timeout=15,
                verify=False
            )

            logging.info(f"ID {id} 尝试 {attempt + 1}: 状态码 {response.status_code}")

            # 检测反爬虫
            is_detected, detection_reason = anti_detection_manager.detect_anti_crawler(response)

            if is_detected:
                logging.warning(f"ID {id} 尝试 {attempt + 1}: 检测到反爬虫 - {detection_reason}")

                # 标记当前代理失效并获取新代理
                proxy_manager.mark_proxy_failed(current_proxy)
                current_proxy = None

                # 获取新令牌
                pxvid = anti_detection_manager.get_pxvid_token(proxy_manager)

                # 增加延时
                time.sleep(random.uniform(10, 20))
                continue

            # 检查响应是否成功
            if response.status_code == 200:
                html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')

                # 检查是否包含产品数据
                if '"Brand","name":"' in html or '"productName":"' in html:
                    logging.info(f"ID {id}: 成功获取产品数据")

                    # 提取产品信息
                    row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
                    row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"',html) else ''
                    row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
                    row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
                    row_data['Link'] = re.findall('\{"@type":"Offer","url":"(.*?)",', html)[0] if re.findall( '\{"@type":"Offer","url":"(.*?)",', html) else ''
                    row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","', html)[0] if re.findall(',"sellerDisplayName":"(.*?)","', html) else ''
                    row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"', html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"', html) else ''
                    row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",', html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",', html) else ''
                    row_data['wfs'] = re.findall('"wfsEnabled":(.*?),', html)[0] if re.findall('"wfsEnabled":(.*?),',html) else ''
                    row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"', html)[0] if re.findall('"shipPrice":{"price":(.*?),"', html) else ''
                    row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html) else ''
                    row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","', html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","', html) else ''

                    if row_data['zy'] == '':
                        row_data['Brand'] = 'ID失效或缺货'

                    # 提取图片链接
                    image_result = {'ID': id}
                    all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
                    if all_images_match:
                        try:
                            images_data = json.loads(f'[{all_images_match.group(1)}]')
                            for idx, img in enumerate(images_data[:20], 1):
                                if 'url' in img:
                                    image_result[f'Image_{idx}'] = img['url']
                        except Exception as e:
                            logging.error(f"ID {id}: 图片解析失败 - {str(e)}")
                    row_data.update(image_result)

                    logging.info(f"ID {id}: 处理成功")
                    break
                else:
                    logging.warning(f"ID {id} 尝试 {attempt + 1}: 响应中无产品数据")
                    if attempt == max_retries - 1:
                        row_data['Brand'] = 'ID失效或缺货'

            elif response.status_code == 404:
                logging.info(f"ID {id}: 产品不存在 (404)")
                row_data['Brand'] = 'ID失效或缺货'
                break
            else:
                logging.warning(f"ID {id} 尝试 {attempt + 1}: 异常状态码 {response.status_code}")
                if attempt == max_retries - 1:
                    row_data['Brand'] = f'请求失败 ({response.status_code})'

        except Exception as e:
            logging.error(f"ID {id} 尝试 {attempt + 1}: 请求异常 - {str(e)}")

            # 如果是代理相关错误，标记代理失效
            if "proxy" in str(e).lower() or "connection" in str(e).lower():
                if current_proxy:
                    proxy_manager.mark_proxy_failed(current_proxy)
                    current_proxy = None

            if attempt == max_retries - 1:
                row_data['Brand'] = '请求失败'

    logging.info(f"ID {id}: 处理完成 - {row_data.get('Brand', '未知状态')}")
    return row_data


def process_id(id, vid_queue):
    """保留原有函数作为备用"""
    row_data = {'ID': id}
    max_retries = 3

    # 从队列获取pxvid（每个vid最多使用5次）
    try:
        pxvid = vid_queue.get_nowait()
    except queue.Empty:
        row_data['Brand'] = 'No pxvid available'
        print(f"ID {id}: 无可用pxvid")
        return row_data

    for attempt in range(max_retries):
        try:
            url = f"https://www.walmart.com/ip/{id}"
            cookie = {"_pxvid": pxvid}
            time.sleep(random.randint(5, 10))

            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=get_ip_address(),
                timeout=10
            )

            if response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR':
                html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')
                # 提取产品信息
                row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
                row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"',html) else ''
                row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
                row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
                row_data['Link'] = re.findall('\{"@type":"Offer","url":"(.*?)",', html)[0] if re.findall( '\{"@type":"Offer","url":"(.*?)",', html) else ''
                row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","', html)[0] if re.findall(',"sellerDisplayName":"(.*?)","', html) else ''
                row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"', html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"', html) else ''
                row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",', html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",', html) else ''
                row_data['wfs'] = re.findall('"wfsEnabled":(.*?),', html)[0] if re.findall('"wfsEnabled":(.*?),',html) else ''
                row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"', html)[0] if re.findall('"shipPrice":{"price":(.*?),"', html) else ''
                row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html) else ''
                row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","', html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","', html) else ''
                # print(row_data['Link'])
                # print(row_data['price'])
                if row_data['zy'] == '':
                    row_data['Brand'] = 'ID失效或缺货'

                # 提取图片链接
                image_result = {'ID': id}
                all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
                if all_images_match:
                    try:
                        images_data = json.loads(f'[{all_images_match.group(1)}]')
                        for idx, img in enumerate(images_data[:20], 1):
                            if 'url' in img:
                                image_result[f'Image_{idx}'] = img['url']
                    except Exception as e:
                        print(f"图片解析失败 ID: {id} - {str(e)}")
                row_data.update(image_result)
                break  # 请求成功，退出重试循环
            else:
                print(f"ID {id} 状态码 {response.status_code}, 第{attempt + 1}次重试")
                if response.status_code == 404 and attempt == 2:
                    row_data['Brand'] = 'ID失效或缺货'
        except Exception as e:
            print(f"ID {id} 请求异常: {str(e)}，第 {attempt + 1} 次重试")
            if attempt == 2:
                row_data['Brand'] = '请求失败'
    print(row_data)
    return row_data


def request_data_enhanced():
    """增强版的数据请求函数"""
    logging.info("="*60)
    logging.info("开始增强版数据采集 - 集成代理池和反爬虫功能")
    logging.info("="*60)

    # 初始化管理器
    proxy_manager = ProxyManager()
    anti_detection_manager = AntiDetectionManager()

    # 显示代理池状态
    stats = proxy_manager.get_stats()
    logging.info(f"代理池状态: 总计 {stats['total']}, 可用 {stats['available']}, 失效 {stats['failed']}")

    # 先触发反检测
    logging.info("步骤1: 触发反检测机制")
    trigger_anti_detection(proxy_manager, anti_detection_manager)

    # 获取ID列表
    logging.info("步骤2: 加载ID列表和pxvid队列")
    ids = get_id()
    vid_queue = load_pxvid_queue()

    logging.info(f"总共需要处理 {len(ids)} 个ID")

    all_results = []

    # 使用较少的并发数以避免过度触发反爬虫
    max_workers = 5
    logging.info(f"步骤3: 开始数据采集 (并发数: {max_workers})")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 使用增强版的处理函数
        futures = [
            executor.submit(process_id_enhanced, id, vid_queue, proxy_manager, anti_detection_manager)
            for id in ids
        ]

        completed = 0
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                all_results.append(result)
                completed += 1

                progress = (completed / len(ids)) * 100
                logging.info(f"进度: {progress:.1f}% ({completed}/{len(ids)}) - ID {result['ID']}: {result.get('Brand', '未知')}")

                # 每处理10个ID显示一次代理池状态
                if completed % 10 == 0:
                    stats = proxy_manager.get_stats()
                    logging.info(f"代理池状态更新: 可用 {stats['available']}/{stats['total']}")

            except Exception as e:
                logging.error(f"处理任务时出现异常: {str(e)}")
                traceback.print_exc()

    # 生成结果文件
    logging.info("步骤4: 保存结果")
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs','yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    output_file = f'enhanced-result-{datetime.today().month}-{datetime.today().day}-{name}'
    df.to_excel(output_file, index=False)

    # 统计结果
    success_count = len([r for r in all_results if r.get('Brand') and r['Brand'] not in ['请求失败', 'ID失效或缺货', 'No pxvid available', '无可用代理']])
    final_stats = proxy_manager.get_stats()

    logging.info("="*60)
    logging.info("数据采集完成!")
    logging.info(f"结果文件: {output_file}")
    logging.info(f"成功采集: {success_count}/{len(ids)} ({success_count/len(ids)*100:.1f}%)")
    logging.info(f"最终代理状态: 可用 {final_stats['available']}/{final_stats['total']}")
    logging.info("="*60)


def request_data():
    """保留原有函数作为备用"""
    ids = get_id()
    all_results = []

    # 加载pxvid队列
    vid_queue = load_pxvid_queue()

    with ThreadPoolExecutor(max_workers=100) as executor:
        # 传递vid_queue到每个线程
        futures = [executor.submit(process_id, id, vid_queue) for id in ids]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            all_results.append(result)
            print(f"已完成ID {result['ID']} 的数据采集")

    # 生成结果文件
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs','yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    df.to_excel(f'result-{datetime.today().month}-{datetime.today().day}-{name}', index=False)
    print(f"结果已保存到 result-{datetime.today().month}-{datetime.today().day}-{name}")


def test_anti_detection():
    """测试反检测功能"""
    logging.info("="*60)
    logging.info("反检测测试模式")
    logging.info("="*60)

    proxy_manager = ProxyManager()
    anti_detection_manager = AntiDetectionManager()

    # 显示代理池状态
    stats = proxy_manager.get_stats()
    logging.info(f"代理池状态: 总计 {stats['total']}, 可用 {stats['available']}")

    # 测试代理
    logging.info("测试随机代理...")
    proxy = proxy_manager.get_random_proxy()
    if proxy:
        logging.info(f"获取到代理: {proxy['info']['host']}:{proxy['info']['port']}")

    # 测试令牌获取
    logging.info("测试令牌获取...")
    token = anti_detection_manager.get_pxvid_token(proxy_manager)
    if token:
        logging.info(f"获取到令牌: {token[:15]}...")

    # 触发反检测
    trigger_anti_detection(proxy_manager, anti_detection_manager)

    # 测试单个ID处理
    test_id = "123456789"
    logging.info(f"测试处理ID: {test_id}")

    vid_queue = queue.Queue()
    if token:
        vid_queue.put(token)

    result = process_id_enhanced(test_id, vid_queue, proxy_manager, anti_detection_manager)
    logging.info(f"测试结果: {result}")


if __name__ == '__main__':
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 测试模式
        test_anti_detection()
    else:
        # 正常运行模式
        time1 = time.time()

        print("选择运行模式:")
        print("1. 增强版 (使用代理池和反爬虫功能)")
        print("2. 原版 (使用原有代理)")

        choice = input("请输入选择 (1/2): ").strip()

        if choice == '1':
            request_data_enhanced()
        else:
            request_data()

        elapsed_time = time.time() - time1
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        print(f"总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
