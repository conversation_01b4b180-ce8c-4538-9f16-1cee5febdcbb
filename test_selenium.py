#!/usr/bin/env python
# coding: utf-8

import time
import urllib3
from 队列 import create_driver

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

try:
    print("正在初始化带代理的Selenium WebDriver...")
    driver = create_driver()
    
    print("正在访问httpbin.org以测试IP...")
    driver.get("https://httpbin.org/ip")
    time.sleep(3)
    
    # 获取页面源代码
    page_source = driver.page_source
    print("页面内容:", page_source)
    
    print("正在访问Walmart测试站点...")
    driver.get("https://www.walmart.com")
    time.sleep(5)
    
    # 打印Walmart页面标题
    print("Walmart页面标题:", driver.title)
    
    # 获取cookies
    cookies = driver.get_cookies()
    print("获取到的Cookies:")
    for cookie in cookies:
        if '_px' in cookie['name']:
            print(f"{cookie['name']}: {cookie['value']}")
    
    print("测试完成，关闭浏览器.")
    driver.quit()
    
except Exception as e:
    print("测试失败:", str(e))
    try:
        driver.quit()
    except:
        pass 