# coding: utf-8
# 高级反爬虫系统测试脚本

import logging
import time
import traceback
from advanced_anti_detection import (
    AdvancedProxyManager,
    UndetectedBrowserManager, 
    WalmartAntiDetectionHandler
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_proxy_manager():
    """测试代理管理器"""
    print("🔧 测试代理管理器...")
    try:
        proxy_manager = AdvancedProxyManager()
        stats = proxy_manager.get_stats()
        print(f"✅ 代理加载成功: {stats}")
        
        # 测试获取最佳代理
        best_proxy = proxy_manager.get_best_proxy()
        if best_proxy:
            print(f"✅ 获取最佳代理: {best_proxy['host']}:{best_proxy['port']}")
            return True
        else:
            print("❌ 无法获取代理")
            return False
    except Exception as e:
        print(f"❌ 代理管理器测试失败: {str(e)}")
        return False

def test_browser_creation():
    """测试浏览器创建"""
    print("\n🌐 测试浏览器创建...")
    proxy_manager = None
    browser_manager = None
    
    try:
        proxy_manager = AdvancedProxyManager()
        browser_manager = UndetectedBrowserManager(proxy_manager)
        
        # 创建浏览器（不使用代理，避免网络问题）
        driver = browser_manager.create_driver(use_proxy=False, headless=True)
        
        if driver:
            print("✅ 浏览器创建成功")
            
            # 测试访问简单页面
            driver.get("https://httpbin.org/user-agent")
            time.sleep(3)
            
            page_source = driver.page_source
            if "User-Agent" in page_source:
                print("✅ 页面访问成功")
                return True
            else:
                print("❌ 页面访问失败")
                return False
        else:
            print("❌ 浏览器创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器测试失败: {str(e)}")
        print(traceback.format_exc())
        return False
    finally:
        if browser_manager:
            browser_manager.close_driver()

def test_walmart_access():
    """测试Walmart访问"""
    print("\n🛒 测试Walmart访问...")
    proxy_manager = None
    browser_manager = None
    handler = None
    
    try:
        proxy_manager = AdvancedProxyManager()
        browser_manager = UndetectedBrowserManager(proxy_manager)
        handler = WalmartAntiDetectionHandler(browser_manager, proxy_manager)
        
        # 初始化会话
        if handler.initialize_session():
            print("✅ Walmart会话初始化成功")
            
            # 测试访问商品页面
            test_url = "https://www.walmart.com/ip/123456789"
            success, page_source = handler.smart_page_load(test_url, max_retries=1)
            
            if success:
                print("✅ 商品页面访问成功")
                
                # 检测反爬虫状态
                current_url = handler.driver.current_url
                is_detected, reason = handler.detect_anti_crawler_response(current_url, page_source)
                
                if is_detected:
                    print(f"⚠️ 检测到反爬虫: {reason}")
                    print("这是正常的，说明检测功能工作正常")
                else:
                    print("✅ 未检测到反爬虫，页面正常")
                
                return True
            else:
                print("❌ 商品页面访问失败")
                return False
        else:
            print("❌ Walmart会话初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ Walmart访问测试失败: {str(e)}")
        print(traceback.format_exc())
        return False
    finally:
        if handler:
            handler.cleanup()

def test_anti_detection_trigger():
    """测试反爬虫触发"""
    print("\n🎯 测试反爬虫触发...")
    proxy_manager = None
    browser_manager = None
    handler = None
    
    try:
        proxy_manager = AdvancedProxyManager()
        browser_manager = UndetectedBrowserManager(proxy_manager)
        handler = WalmartAntiDetectionHandler(browser_manager, proxy_manager)
        
        if handler.initialize_session():
            print("✅ 会话初始化成功")
            
            # 快速连续访问多个页面来触发反爬虫
            test_urls = [
                "https://www.walmart.com/ip/111111111",
                "https://www.walmart.com/ip/222222222", 
                "https://www.walmart.com/ip/333333333"
            ]
            
            detected_count = 0
            
            for i, url in enumerate(test_urls, 1):
                print(f"快速请求 {i}: {url}")
                
                try:
                    handler.driver.get(url)
                    time.sleep(1)  # 很短的延时，模拟快速请求
                    
                    current_url = handler.driver.current_url
                    page_source = handler.driver.page_source
                    
                    is_detected, reason = handler.detect_anti_crawler_response(current_url, page_source)
                    
                    if is_detected:
                        detected_count += 1
                        print(f"🔴 触发反爬虫: {reason}")
                    else:
                        print(f"🟢 未触发反爬虫")
                        
                except Exception as e:
                    print(f"请求异常: {str(e)}")
            
            if detected_count > 0:
                print(f"✅ 成功触发反爬虫 {detected_count} 次")
                return True
            else:
                print("⚠️ 未能触发反爬虫（可能需要更激进的策略）")
                return False
        else:
            print("❌ 会话初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 反爬虫触发测试失败: {str(e)}")
        return False
    finally:
        if handler:
            handler.cleanup()

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始高级反爬虫系统综合测试")
    print("=" * 60)
    
    tests = [
        ("代理管理器", test_proxy_manager),
        ("浏览器创建", test_browser_creation),
        ("Walmart访问", test_walmart_access),
        ("反爬虫触发", test_anti_detection_trigger)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            results[test_name] = {
                'success': result,
                'duration': end_time - start_time
            }
            
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status} (耗时: {end_time - start_time:.1f}秒)")
            
        except Exception as e:
            results[test_name] = {
                'success': False,
                'error': str(e),
                'duration': 0
            }
            print(f"{test_name}: ❌ 异常 - {str(e)}")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result['success'] else "❌"
        duration = result.get('duration', 0)
        print(f"{status} {test_name:<20} ({duration:.1f}秒)")
    
    print(f"\n总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪")
    elif passed >= total * 0.75:
        print("⚠️ 大部分测试通过，系统基本可用")
    else:
        print("❌ 多个测试失败，请检查配置")
    
    return passed == total

def quick_test():
    """快速测试"""
    print("⚡ 快速测试...")
    
    try:
        # 只测试基础功能
        proxy_manager = AdvancedProxyManager()
        stats = proxy_manager.get_stats()
        
        if stats['total'] > 0:
            print(f"✅ 系统基础功能正常，代理池: {stats}")
            return True
        else:
            print("❌ 代理池为空")
            return False
            
    except Exception as e:
        print(f"❌ 快速测试失败: {str(e)}")
        return False

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'quick':
        quick_test()
    else:
        run_comprehensive_test()
