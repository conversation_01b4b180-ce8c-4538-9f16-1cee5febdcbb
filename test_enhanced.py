# coding: utf-8
# 测试增强版爬虫功能

import sys
import os
import importlib.util

# 动态导入1.py模块
spec = importlib.util.spec_from_file_location("main_module", "1.py")
main_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(main_module)

# 导入需要的类和函数
ProxyManager = main_module.ProxyManager
AntiDetectionManager = main_module.AntiDetectionManager
trigger_anti_detection = main_module.trigger_anti_detection
import logging
import time
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_proxy_manager():
    """测试代理管理器"""
    print("="*50)
    print("测试代理管理器")
    print("="*50)
    
    try:
        proxy_manager = ProxyManager()
        stats = proxy_manager.get_stats()
        print(f"代理池统计: {stats}")
        
        # 测试获取随机代理
        for i in range(3):
            proxy = proxy_manager.get_random_proxy()
            if proxy:
                print(f"代理 {i+1}: {proxy['info']['host']}:{proxy['info']['port']}")
            else:
                print(f"代理 {i+1}: 无可用代理")
        
        return True
    except Exception as e:
        print(f"代理管理器测试失败: {str(e)}")
        return False

def test_anti_detection():
    """测试反检测管理器"""
    print("="*50)
    print("测试反检测管理器")
    print("="*50)
    
    try:
        proxy_manager = ProxyManager()
        anti_detection_manager = AntiDetectionManager()
        
        # 测试生成headers
        headers = anti_detection_manager.generate_headers()
        print(f"生成的Headers User-Agent: {headers.get('User-Agent', 'N/A')}")
        
        # 测试获取令牌
        print("尝试获取pxvid令牌...")
        token = anti_detection_manager.get_pxvid_token(proxy_manager)
        if token:
            print(f"成功获取令牌: {token[:15]}...")
        else:
            print("获取令牌失败")
        
        return True
    except Exception as e:
        print(f"反检测管理器测试失败: {str(e)}")
        return False

def test_trigger_detection():
    """测试触发反检测"""
    print("="*50)
    print("测试触发反检测")
    print("="*50)
    
    try:
        proxy_manager = ProxyManager()
        anti_detection_manager = AntiDetectionManager()
        
        result = trigger_anti_detection(proxy_manager, anti_detection_manager)
        print(f"触发反检测结果: {result}")
        
        return True
    except Exception as e:
        print(f"触发反检测测试失败: {str(e)}")
        return False

def test_single_request():
    """测试单个请求"""
    print("="*50)
    print("测试单个请求")
    print("="*50)
    
    try:
        proxy_manager = ProxyManager()
        anti_detection_manager = AntiDetectionManager()
        
        # 获取代理和令牌
        proxy = proxy_manager.get_random_proxy()
        token = anti_detection_manager.get_pxvid_token(proxy_manager)
        
        if not proxy:
            print("无可用代理，跳过请求测试")
            return False
        
        # 测试请求
        url = "https://www.walmart.com/ip/123456789"
        headers = anti_detection_manager.generate_headers()
        cookies = {}
        if token:
            cookies["_pxvid"] = token
        
        print(f"使用代理: {proxy['info']['host']}:{proxy['info']['port']}")
        print(f"请求URL: {url}")
        
        response = requests.get(
            url,
            headers=headers,
            cookies=cookies,
            proxies={'http': proxy['http'], 'https': proxy['https']},
            timeout=10,
            verify=False
        )
        
        print(f"响应状态码: {response.status_code}")
        
        # 检测反爬虫
        is_detected, reason = anti_detection_manager.detect_anti_crawler(response)
        print(f"反爬虫检测: {is_detected} - {reason}")
        
        return True
    except Exception as e:
        print(f"单个请求测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试增强版爬虫功能...")
    
    tests = [
        ("代理管理器", test_proxy_manager),
        ("反检测管理器", test_anti_detection),
        ("触发反检测", test_trigger_detection),
        ("单个请求", test_single_request)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"{test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
        
        time.sleep(2)  # 测试间隔
    
    # 显示测试结果
    print("\n" + "="*50)
    print("测试结果汇总")
    print("="*50)
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 通过")

if __name__ == '__main__':
    main()
