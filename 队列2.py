# coding: utf-8
# Project:fenggong_imagelink
# File:1.py
# Author:白茶
# Date: 2025/4/3 上午10:00
# IDE:PyCharm

import concurrent
import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
from concurrent.futures import ThreadPoolExecutor
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import base64
import sys

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 始终使用代理连接
# USE_PROXY 变量已删除，代码默认使用代理


def get_id():
    df = pd.read_excel('1.xlsx')
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids


def get_ip_address():
    proxyAddr = "overseas.tunnel.qg.net:16774"
    authKey = "5KOSP2G1"
    password = "21E8BB464C73"
    return {
        "server": proxyAddr,
        "user": authKey,
        "password": password
    }


def md5_encrypt(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()


def setup_browser():
    """设置并返回Chrome浏览器实例，使用代理连接"""
    options = Options()
    
    # 设置User-Agent
    v1 = random.randint(100, 134)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)
    user_agent = f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36"
    options.add_argument(f'user-agent={user_agent}')
    
    # 其他设置
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-extensions')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])  # 减少日志输出
    options.add_experimental_option('useAutomationExtension', False)
    
    try:
        print("创建浏览器实例...")
        
        # 获取代理信息
        proxy_info = get_ip_address()
        print(f"使用代理: {proxy_info['server']}")
        
        # 设置代理服务器
        options.add_argument(f'--proxy-server={proxy_info["server"]}')
        
        # 创建浏览器实例
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        
        # 设置代理认证
        print("设置代理认证...")
        driver.execute_cdp_cmd('Network.enable', {})
        auth_string = f"{proxy_info['user']}:{proxy_info['password']}"
        auth_base64 = base64.b64encode(auth_string.encode()).decode()
        driver.execute_cdp_cmd('Network.setExtraHTTPHeaders', {
            "headers": {"Proxy-Authorization": f"Basic {auth_base64}"}
        })
        
        # 设置超时 - 增加超时时间以适应代理环境
        driver.set_page_load_timeout(60)
        print("浏览器实例创建成功!")
        
        # 修改navigator属性以避免检测
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
            '''
        })
        
        return driver
        
    except Exception as e:
        print(f"浏览器初始化失败: {str(e)}")
        # 添加更详细的错误信息
        print(f"详细错误: {type(e).__name__}")
        import traceback
        print(traceback.format_exc())
        sys.exit(1)


def extract_data_from_page(driver, id):
    """从页面提取数据"""
    row_data = {'ID': id}
    
    try:
        # 等待页面加载完成
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 使用JavaScript获取页面HTML内容
        html = driver.execute_script("return document.documentElement.outerHTML;")
        
        # 提取产品信息（保留原有正则提取逻辑，因为它可以处理动态加载的JSON数据）
        row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
        row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"', html) else ''
        row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
        row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
        row_data['Link'] = (re.findall('{"@type":"Offer","url":"(.*?)",', html)[0] + '?classType=REGULAR&from=/search') if re.findall('{"@type":"Offer","url":"(.*?)",', html) else ''
        row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","', html)[0] if re.findall(',"sellerDisplayName":"(.*?)","', html) else ''
        row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"', html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"(.*?),"', html) else ''
        row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",', html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",', html) else ''
        row_data['wfs'] = re.findall('"wfsEnabled":(.*?),', html)[0] if re.findall('"wfsEnabled":(.*?),', html) else ''
        row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"', html)[0] if re.findall('"shipPrice":{"price":(.*?),"', html) else ''
        row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html) else ''
        row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","', html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","', html) else ''
        
        # 提取图片链接
        image_result = {'ID': id}
        all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
        if all_images_match:
            try:
                images_data = json.loads(f'[{all_images_match.group(1)}]')
                for idx, img in enumerate(images_data[:20], 1):  # 最多取20张图片
                    if 'url' in img:
                        image_result[f'Image_{idx}'] = img['url']
            except Exception as e:
                print(f"图片解析失败 ID: {id} - {str(e)}")
        
        # 合并数据到同一行
        row_data.update(image_result)
        
    except Exception as e:
        print(f"数据提取失败 ID: {id} - {str(e)}")
        row_data['Brand'] = '数据提取失败'
    
    return row_data


def process_id(id):
    """处理单个ID，获取产品信息"""
    row_data = {'ID': id}
    max_retries = 3
    driver = None
    
    try:
        driver = setup_browser()
        
        for attempt in range(max_retries):
            try:
                url = f"https://www.walmart.com/ip/{id}"
                print(f"正在访问: {url}")
                
                # 访问产品页面
                driver.get(url)
                
                # 输出页面标题，帮助诊断
                print(f"页面标题: {driver.title}")
                
                # 等待页面加载（添加延迟避免反爬）
                time.sleep(11)
                
                # 检查页面是否成功加载
                if "walmart" in driver.title.lower():
                    print(f"成功加载Walmart页面: {driver.title}")
                    row_data = extract_data_from_page(driver, id)
                    break
                else:
                    print(f"ID {id} 页面加载不正确，标题: {driver.title}，第{attempt + 1}次重试")
                    # 保存页面源代码以便调试
                    with open(f"debug_page_{id}_{attempt}.html", "w", encoding="utf-8") as f:
                        f.write(driver.page_source)
                    print(f"已保存调试页面源码到 debug_page_{id}_{attempt}.html")
                    time.sleep(5)
                    
            except Exception as e:
                print(f"ID {id} 请求异常: {str(e)}，第 {attempt + 1} 次重试")
                if attempt == max_retries - 1:
                    row_data['Brand'] = '请求失败'
                time.sleep(5)
                
    except Exception as e:
        print(f"浏览器初始化失败 ID: {id} - {str(e)}")
        row_data['Brand'] = '浏览器初始化失败'
        
    finally:
        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass
                
    print(row_data)
    return row_data


def request_data():
    ids = get_id()
    all_results = []

    # 减少并发数以防止资源耗尽
    with ThreadPoolExecutor(max_workers=1) as executor:
        futures = [executor.submit(process_id, id) for id in ids]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            all_results.append(result)
            print(f"已完成ID {result['ID']} 的数据采集")

    # 动态生成列（产品信息列 + 图片列）
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs', 'yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    df.to_excel('combined_results-usItemIds410-2.xlsx', index=False)
    print("结果已保存到 combined_results-usItemIds410-2.xlsx")


def test_browser_connectivity():
    """测试浏览器网络连接"""
    print("=" * 50)
    print("开始测试浏览器网络连接...")
    print("=" * 50)
    
    # 尝试不同的URL进行测试
    test_urls = [
        "https://www.baidu.com",  # 百度
        "https://www.bing.com",   # 必应
        "https://www.qq.com",     # 腾讯
        "https://www.walmart.com" # 最终目标网站
    ]
    
    driver = None
    try:
        driver = setup_browser()
        print("浏览器启动成功")
        
        for url in test_urls:
            try:
                print(f"\n开始访问 {url}...")
                driver.get(url)
                
                print(f"页面标题: {driver.title}")
                print(f"当前URL: {driver.current_url}")
                print(f"页面内容长度: {len(driver.page_source)}")
                
                # 保存页面源码供分析
                filename = url.replace("https://", "").replace("http://", "").replace(".", "_").replace("/", "_") + ".html"
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
                print(f"已保存页面源码到 {filename}")
                
                print(f"✓ 成功访问 {url}")
            except Exception as e:
                print(f"✗ 访问 {url} 失败: {str(e)}")
        
        # 尝试访问最终目标网站
        try:
            final_url = "https://www.walmart.com/ip/123456789"  # 使用一个示例产品ID
            print(f"\n尝试访问产品页面: {final_url}")
            driver.get(final_url)
            print(f"产品页面标题: {driver.title}")
            
            if "walmart" in driver.title.lower():
                print("✓ 成功访问Walmart产品页面")
                return True
            else:
                print("✗ Walmart页面加载异常")
                return False
        except Exception as e:
            print(f"✗ 访问Walmart产品页面失败: {str(e)}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False
    finally:
        if driver:
            driver.quit()
            print("浏览器已关闭")


if __name__ == '__main__':
    print("=" * 50)
    print("Walmart产品爬取工具启动")
    print("=" * 50)
    print("代理模式: 已启用")
    
    time1 = time.time()
    # 先测试网络连接
    success = test_browser_connectivity()
    
    if success:
        print("\n网络连接测试成功！正在开始数据采集...")
        request_data()
    else:
        print("\n网络连接测试失败！")
        print("请尝试以下方法解决:")
        print("1. 检查网络连接")
        print("2. 更新代理服务器信息")
        print("3. 检查防火墙或安全软件设置")
    
    print(f"总用时: {time.time() - time1:.2f}秒")